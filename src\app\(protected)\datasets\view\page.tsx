"use client";

import type React from "react";
import { <PERSON><PERSON>, <PERSON>, Typography, Box } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import type { RootState } from "../../../../store/store";
import { Suspense, useEffect, useState } from "react";
import Papa from "papaparse";
import { dataSetFilePreview } from "../../../../services/dataSetapiService";
import { formatDate } from "../../../../constants/fieldtype";
import FolderIcon from "@mui/icons-material/Folder";
import Image from "next/image";

// Define types for better type safety
interface DatasetItem {
  id: string;
  "Dataset Name"?: string;
  "Upload Dataset"?: Array<Record<string, string>>;
  [key: string]: unknown;
}

interface FolderStructureItem {
  type: string;
  name: string;
  preview?: string[];
  children?: FolderStructureItem[];
}

interface FolderStructure {
  type: "folder";
  structure: FolderStructureItem[];
  files: string[];
  path: string;
  filePreviews: Record<string, string>;
}

const ViewDataSetDetails: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const datasetListDetails = useSelector(
    (state: RootState) => state.dataset?.datasetList || []
  );
  const datasetId = searchParams?.get("id");
  const selectedDataset = datasetListDetails.find(
    (ds: { id: string }) => ds.id === datasetId
  ) as DatasetItem | undefined;
  const [previewData, setPreviewData] = useState<string[][]>([]);
  const [loadingPreview, setLoadingPreview] = useState(false);
  const [fileSize, setFileSize] = useState<number | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [, setPreviewError] = useState<string | null>(null);

  const [folderStructure, setFolderStructure] =
    useState<FolderStructure | null>(null);
  const [folderHistory, setFolderHistory] = useState<FolderStructure[]>([]);

  const handleBackFolder = () => {
    if (folderHistory.length > 0) {
      const previous = [...folderHistory];
      const last = previous.pop();
      setFolderHistory(previous);
      setFolderStructure(last || null);
    }
  };

  console.log("folderStructure", folderStructure);

  console.log("previewData", previewData);
  useEffect(() => {
    if (!selectedDataset) {
      router.push("/datasets");
    }
  }, [selectedDataset, router]);
  const handleDataset = () => {
    router.push("/datasets");
  };
  const handlePreview = (fileName: string | undefined) => async () => {
    if (!fileName) return;

    setLoadingPreview(true);
    setPreviewError(null);
    setFolderStructure(null);
    setPreviewData([]);

    try {
      setLoadingPreview(true);
      const res = await dataSetFilePreview(fileName);
      if (res.status < 200 || res.status >= 300)
        throw new Error("Failed to fetch preview");
      const data = res.data;

      // if (data?.fileType === "zip") {
      setFolderStructure(data);

      if (data?.fileSize) {
        setFileSize(Number(data.fileSize));
      }

      if (data?.fileType) {
        setFileType(data.fileType);
      }
      // } else
      if (data?.filePreview) {
        const csvString = data.filePreview;
        const parsed = Papa.parse(csvString.trim(), { skipEmptyLines: true });
        if (parsed.errors.length) throw new Error("CSV Parsing error");
        setPreviewData(parsed.data as string[][]);
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : typeof err === "string"
          ? err
          : "Something went wrong";
      setPreviewError(errorMessage);
    } finally {
      setLoadingPreview(false);
    }
  };
  const handleFolderClick = (folder: FolderStructureItem) => () => {
    if (folder.type === "folder" && folder.children) {
      setFolderHistory((prev) => [...prev, folderStructure!]);
      setFolderStructure({
        type: "folder",
        structure: folder.children,
        files: [],
        path: `${folderStructure?.path || ""}${folder.name}/`,
        filePreviews: {},
      });
    }
  };

  return (
    <>
      <div className="min-h-screen p-6">
        <Box className="flex items-center justify-between mb-4">
          <Typography
            variant="h5"
            fontWeight="bold"
            style={{ color: "#000000" }}
            sx={{ whiteSpace: "nowrap" }}
          >
            Dataset Details
          </Typography>
          <Button
            onClick={handleDataset}
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            className="text-transform-none"
            sx={{
              borderColor: "orange",
              color: "black",
              "&:hover": {
                borderColor: "darkorange",
                backgroundColor: "rgba(255,165,0,0.1)",
              },
            }}
          >
            Back
          </Button>
        </Box>
        {selectedDataset && (
          <Card className="p-6 rounded-2xl shadow-sm space-y-6 bg-white">
            <Box className="bg-[#FFF4ED] p-6 rounded-md">
              <Typography
                variant="h5"
                fontWeight="bold"
                className="mb-2 text-capitalize"
                sx={{
                  fontSize: {
                    xs: "1.1rem", // mobile (16px)
                    sm: "1.25rem", // small screen (20px)
                    md: "1.5rem", // medium screen and up (24px)
                  },
                }}
              >
                {selectedDataset["Dataset Name"] || "Dataset"}
              </Typography>
            </Box>
            <Box className="border border-orange-300 rounded-md p-6 space-y-2">
              {Object.entries(selectedDataset)
                .filter(
                  ([key]) =>
                    ![
                      "Upload Dataset",
                      "like_count",
                      "id",
                      "type",
                      "organization_id",
                      "updated_at",
                      "average_rating",
											"status",
                    ].includes(key)
                )
                .map(([key, value]) => {
                  let displayValue = value;
                  if (key === "created_at") {
                    const date = new Date(value as string);
                    const options: Intl.DateTimeFormatOptions = {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                      hour12: true,
                    };
                    // Format: "19 Jun 2025, 11:04 am"
                    displayValue = date
                      .toLocaleString("en-GB", options)
                      .replace(/(\d{2}):(\d{2})/, (match, h, m) => `${h}:${m}`)
                      .replace(",", "")
                      .replace(/(\d{2}) (\w{3}) (\d{4})/, "$1 $2 $3,")
                      .replace(/\s+AM$/, " am")
                      .replace(/\s+PM$/, " pm");
                  }
                  return (
                    <Box key={key} className="flex gap-1 flex-wrap">
                      <Typography
                        variant="body1"
                        fontWeight="bold"
                        className="text-gray-600"
                      >
                        {key.replace(/_/g, " ")}:
                      </Typography>
                      <Typography
                        variant="body1"
                        className="text-gray-800 space-x-1"
                      >
                        {Array.isArray(displayValue)
                          ? displayValue.join(", ")
                          : typeof displayValue === "object" &&
                            displayValue !== null
                          ? JSON.stringify(displayValue)
                          : String(displayValue || "N/A")}
                      </Typography>
                    </Box>
                  );
                })}
            </Box>

            {Array.isArray(selectedDataset["Upload Dataset"]) &&
              selectedDataset["Upload Dataset"].length > 0 && (
                <Box className="border border-orange-300 rounded-md p-6">
                  <Typography
                    variant="subtitle1"
                    fontWeight="bold"
                    marginBottom={"10px"}
                    className="mb-4"
                  >
                    Files
                  </Typography>

                  {(() => {
                    const columns = [
                      { label: "File Name", key: "Value" },
                      {
                        label: "Uploaded By",
                        key: "Created By",
                        isFromDataset: true,
                      },
                      {
                        label: "Uploaded On",
                        key: "updated_at",
                        isFromDataset: true,
                      },
                    ];

                    return (
                      <>
                        <Box className="w-full overflow-x-auto">
                          <Box className="min-w-[1200px]">
                            <Box className="grid grid-cols-4 font-semibold text-gray-600 border-b pb-2 mb-2 whitespace-nowrap gap-x-6">
                              {columns.map((col) => (
                                <Typography key={col.label}>
                                  {col.label}
                                </Typography>
                              ))}
                              <Box />
                            </Box>

                            {(
                              selectedDataset["Upload Dataset"] as Array<
                                Record<string, string>
                              >
                            )

                              .filter((item) => item.Key === "file_path")
                              .map((file, index) => (
                                <Box
                                  key={`${file?.Value ?? "file"}-${index}`}
                                  className="grid grid-cols-4 items-center text-gray-800 py-2 border-b border-gray-200 last:border-b-0 gap-x-6"
                                >
                                  {columns.map((col) => {
                                    const rawValue = col.isFromDataset
                                      ? selectedDataset[col.key]
                                      : file?.[col.key];

                                    const value =
                                      (col.key === "created_at" ||
                                        col.key === "updated_at") &&
                                      typeof rawValue === "string"
                                        ? formatDate(rawValue)
                                        : rawValue || "N/A";

                                    const displayValue =
                                      col.key === "Value"
                                        ? selectedDataset["Dataset Name"] ||
                                          "N/A"
                                        : String(value);

                                    return (
                                      <Typography
                                        key={col.key}
                                        className={`${
                                          col.key === "Value" ? "truncate" : ""
                                        } whitespace-nowrap px-2 text-sm`}
                                      >
                                        {displayValue}
                                      </Typography>
                                    );
                                  })}
                                  <Box className="px-2">
                                    <Button
                                      variant="contained"
                                      className="text-transform-none"
                                      disabled={loadingPreview}
                                      sx={{
                                        background: loadingPreview
                                          ? "#C5C5C5"
                                          : "linear-gradient(to right, #f97316, #ea580c)",
                                        fontSize: "0.8rem",
                                        px: 2,
                                        py: 1,
                                        borderRadius: 2,
                                        boxShadow: "none",
                                        "&:hover": {
                                          background:
                                            "linear-gradient(to right, #f97316, #ea580c)",
                                        },
                                      }}
                                      onClick={handlePreview(file?.Value)}
                                    >
                                      Preview
                                    </Button>
                                  </Box>
                                </Box>
                              ))}
                          </Box>
                        </Box>
                      </>
                    );
                  })()}
                </Box>
              )}
            {loadingPreview ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mt-4" />
              </div>
            ) : (
              previewData.length > 0 && (
                <>
                 <Typography
                     className="px-6 py-4"
					variant="h6"
            fontWeight="bold"
            style={{ color: "#000000" }}
            sx={{ whiteSpace: "nowrap",backgroundColor:"#fff4ed" }}
                      >
                        Dataset Preview
                      </Typography>
                  <Box className="mt-3">
                    
                    {fileSize && (
   					<Typography
					  variant="subtitle1"
					  className="px-4 py-2 bg-gray-100 font-semibold text-gray-700 inline-block"
					>
					  <span className="font-bold">File Size</span>:{" "}
					  {fileSize !== null
						? fileSize < 1024 * 1024
						  ? (fileSize / 1024).toFixed(2) + " KB"
						  : fileSize < 1024 * 1024 * 1024
						  ? (fileSize / (1024 * 1024)).toFixed(2) + " MB"
						  : (fileSize / (1024 * 1024 * 1024)).toFixed(2) + " GB"
						: "N/A"}
					</Typography>
                    )}

                    {fileType && (
                      <Typography
                        variant="subtitle1"
                        className="px-4 py-2 bg-gray-100 font-semibold text-gray-700 inline-block"
                      >
                        <span className="font-bold">File Type</span>:{" "}
                        {fileType.toUpperCase()}
                      </Typography>
                    )}
                  </Box>

                  <Box className="overflow-auto mt-4 border border-gray-200 rounded-md">
                    <table className="min-w-full divide-y divide-gray-200 text-sm text-left">
                      <thead className="bg-gray-100">
                        <tr>
                          {previewData[0].map((header) => (
                            <th
                              key={header}
                              className="px-4 py-2 font-semibold text-gray-700 whitespace-nowrap"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-100">
                        {previewData.slice(1, 11).map((row) => {
                          const rowKey = row.join("|");
                          return (
                            <tr key={rowKey}>
                              {row.map((cell) => (
                                <td
                                  key={`${rowKey}-${cell}`}
                                  className="px-4 py-2 text-gray-700 whitespace-nowrap"
                                >
                                  {cell}
                                </td>
                              ))}
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </Box>
                </>
              )
            )}

            {folderStructure && (
              <Box className="mt-6">
                {/* <Typography variant="h6" className="mb-2">Folder: {folderStructure.path || "/"}</Typography> */}

                {folderStructure?.structure?.length > 0 && (
                  <Box className="mt-4 ">
					   <Typography
                     className="px-6 py-4"
					variant="h6"
            fontWeight="bold"
            style={{ color: "#000000" }}
            sx={{ whiteSpace: "nowrap",backgroundColor:"#fff4ed" }}
                      >
                        Dataset Preview
                      </Typography>
					{folderHistory.length == 0 && (
					 <Box className="flex justify-start inline-block mb-2 mt-4 ">
                    {fileSize && (
					<Typography
					  variant="subtitle1"
					  className="px-4 py-2  font-semibold text-gray-700 inline-block"
					>
					  <span className="font-bold">File Size</span>:{" "}
					  {fileSize !== null
						? fileSize < 1024 * 1024
						  ? (fileSize / 1024).toFixed(2) + " KB"
						  : fileSize < 1024 * 1024 * 1024
						  ? (fileSize / (1024 * 1024)).toFixed(2) + " MB"
						  : (fileSize / (1024 * 1024 * 1024)).toFixed(2) + " GB"
						: "N/A"}
					</Typography>
                    )}

                    {fileType && (
                      <Typography
                        variant="subtitle1"
                        className="px-4 py-2  font-semibold text-gray-700 inline-block"
                      >
                        <span className="font-bold">File Type</span>:{" "}
                        {fileType.toUpperCase()}
                      </Typography>
                    )}
                  </Box>
					  )}
                    {folderHistory.length > 0 && (
						<Box className="flex justify-between items-center mb-5 mt-4">
                     {/* <Typography
                        variant="subtitle1"
                        className="px-4 py-2 bg-[#fff4ed] font-semibold text-gray-700 "
                      >
                        Dataset Preview
                      </Typography> */}

					<Box className=" inline-block ">
                    {fileSize && (
					<Typography
					  variant="subtitle1"
					  className="px-4 py-2 bg-gray-100 font-semibold text-gray-700 inline-block"
					>
					  <span className="font-bold">File Size</span>:{" "}
					  {fileSize !== null
						? fileSize < 1024 * 1024
						  ? (fileSize / 1024).toFixed(2) + " KB"
						  : fileSize < 1024 * 1024 * 1024
						  ? (fileSize / (1024 * 1024)).toFixed(2) + " MB"
						  : (fileSize / (1024 * 1024 * 1024)).toFixed(2) + " GB"
						: "N/A"}
					</Typography>
                    )}

                    {fileType && (
                      <Typography
                        variant="subtitle1"
                        className="px-4 py-2 bg-gray-100 font-semibold text-gray-700 inline-block"
                      >
                        <span className="font-bold">File Type</span>:{" "}
                        {fileType.toUpperCase()}
                      </Typography>
                    )}
                  </Box>
						
                      {/* <Box className=" mb-4 mt-3">					 */}
					  <Box>
                        <Button
                          onClick={handleBackFolder}
                          variant="outlined"
                          startIcon={<ArrowBackIcon />}
                          className="text-transform-none"
                          sx={{
                            borderColor: "orange",
                            color: "black",
                            "&:hover": {
                              borderColor: "darkorange",
                              backgroundColor: "rgba(255,165,0,0.1)",
                            },
                          }}
                        >
                          Back
                        </Button>
                      </Box>
					  </Box>
                    )}

                    <Box className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {folderStructure.structure.map(
						
                        (item: {
                          type: string;
                          name: string;
                          preview?: string[];
                        }) => {
                          if (item.type === "folder") {
                            return (
								
			                 
                              <Box
                                key={item.name}
                                onClick={handleFolderClick(item)}
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                  cursor: "pointer",
                                  padding: 1,
                                  borderRadius: 2,
                                  "&:hover": {
                                    backgroundColor: "rgba(0, 0, 255, 0.05)",
                                  },
                                }}
                              >
                                <FolderIcon color="primary" />
                                <Typography>{item.name}</Typography>
                              </Box>
							  
                            );
                          } else if (
                            item.type === "image" &&
                            item.preview &&
                            item.preview.length > 0
                          ) {
                            return (
                              <Box
                                key={item.name}
                                className="border border-gray-300 rounded-md p-3 hover:shadow-md transition-shadow"
                              >
                                <div className="flex justify-center">
                                  <Image
                                    src={item.preview[0]}
                                    alt={item.name}
                                    width={150}
                                    height={150}
                                    className="w-full h-auto max-h-[150px] object-contain rounded"
                                  />
                                </div>
                                <Typography
                                  variant="subtitle2"
                                  className="mb-2 font-medium text-center truncate"
                                >
                                  {item.name}
                                </Typography>
                              </Box>
                            );
                          }
                          return null;
                        }
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            )}
          </Card>
        )}
      </div>
    </>
  );
};

export default function ViewDataSetDetailsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ViewDataSetDetails />
    </Suspense>
  );
}
