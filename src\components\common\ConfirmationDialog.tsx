import type React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { tailwindStyles } from "../../styles/tailwindStyles";

interface ConfirmationDialogProps {
  open: boolean;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  title = "Confirm Action",
  description = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      PaperProps={{
        className: "rounded-xl px-6 py-5 bg-white",
      }}
    >
      <div className="flex justify-between items-start">
        <DialogTitle className="text-lg font-semibold text-gray-800 p-0">
          {title}
        </DialogTitle>
        <IconButton onClick={onCancel} size="small">
          <CloseIcon className="text-gray-500" />
        </IconButton>
      </div>

      <DialogContent className=" text-gray-600 text-sm p-0">
        {description}
      </DialogContent>

      <DialogActions className="flex gap-4 pt-4 p-0 justity-center" style={{justifyContent: "center"}}>
        <button
        style={{width:"8rem"}}
          type="button"
          onClick={onCancel}
          className={tailwindStyles.orgCancelButtun}
        >
          {cancelText}
        </button>
        <button
          type="button"
          onClick={onConfirm}
          className={tailwindStyles.orgApproveButton}
        >
          {confirmText}
        </button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationDialog;
