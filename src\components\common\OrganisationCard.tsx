"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Autocomplete,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs from "dayjs";
import { toast } from "react-toastify";
import CustomButton from "../ui/Button";
import {
  addOrganizationSetting,
  getOrganizationList,
  addOrganizationId,
  getGlobalSetting,
} from "../../services/profileApiService";

const OrganisationCard = () => {
  const [organization, setOrganization] = useState(null);
  const [organizationListData, setOrganizationListData] = useState([]);

  const [formValues, setFormValues] = useState({
    archivingDays: "",
    deletionDays: "",
    maxUsers: "",
    maxProjects: "",
    docketSize: "",
    docketUnit: "",
    scheduledTime: "",
  });

  useEffect(() => {
    const fetchFormData = async () => {
      try {
        const res = await getOrganizationList();
        setOrganizationListData(res?.data || []);
      } catch (err) {
        console.error("Failed to fetch form data", err);
        setOrganizationListData([]);
      }
    };

    fetchFormData();
  }, []);

  useEffect(() => {
    const fetchOrganizationSettings = async () => {
      if (!organization?.organization_id) return;

      try {
        const res = await addOrganizationId({
          organization_id: organization.organization_id,
        });

        const settings = res?.data;

        const hasValidOrgSettings = settings?.default_archiving_days || settings?.default_deletion_days;

        if (hasValidOrgSettings) {
          setFormValues({
            archivingDays: settings?.default_archiving_days?.toString() || "",
            deletionDays: settings?.default_deletion_days?.toString() || "",
            maxUsers: settings?.max_users_per_organization?.toString() || "",
            maxProjects: settings?.max_active_projects?.toString() || "",
            docketSize: settings?.max_project_docket_size?.toString() || "",
            docketUnit: settings?.max_project_docket_size_unit || "",
            scheduledTime: settings?.scheduled_evaluation_time || "",
          });
        } else {
          // fallback if values are missing
          await loadGlobalSettings();
        }
      } catch (err) {
        console.error("Error fetching organization settings", err);
        toast.error("Failed to load organization settings. Falling back to global settings.");
        await loadGlobalSettings();
      }
    };

    const loadGlobalSettings = async () => {
      try {
        const res = await getGlobalSetting();
        const data = res?.data;

        if (data) {
          setFormValues({
            archivingDays: data.default_archiving_days?.toString() || "",
            deletionDays: data.default_deletion_days?.toString() || "",
            maxUsers: data.max_users_per_organization?.toString() || "",
            maxProjects: data.max_active_projects?.toString() || "",
            docketSize: data.max_project_docket_size?.toString() || "",
            docketUnit: data.max_project_docket_size_unit || "",
            scheduledTime: data.scheduled_evaluation_time || "",
          });
        }
      } catch (err) {
        console.error("Failed to fetch global settings:", err);
        toast.error("Unable to load global settings as fallback.");
      }
    };

    fetchOrganizationSettings();
  }, [organization]);



  const handleChange = (field: string, value: string) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!organization) {
      toast.warn("Please select an organization.");
      return;
    }

    const payload = {
      organization_id: organization.organization_id,
      default_deletion_days: Number(formValues.deletionDays),
      default_archiving_days: Number(formValues.archivingDays),
      max_active_projects: Number(formValues.maxProjects),
      max_users_per_organization: Number(formValues.maxUsers),
      max_project_docket_size: Number(formValues.docketSize),
      max_project_docket_size_unit: formValues.docketUnit,
      scheduled_evaluation_time: formValues.scheduledTime,
    };

    try {
      await addOrganizationSetting(payload);
      toast.success("Organization settings updated successfully.");
    } catch (error) {
      console.error("Failed to update settings", error);
      toast.error("Error saving organization settings.");
    }
  };

  const isDisabled = !organization;

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className="w-full max-w-5xl mx-auto bg-white rounded-lg border border-[#E2E8F0]">
        <div className="text-lg text-gray-700 font-semibold border-b border-[#E2E8F0] px-6 py-4">
          Organization Settings
        </div>
        <div className="grid grid-cols-2 gap-6 px-6 py-2">
          {/* Organization Selector */}
          <div>
            <Typography className="text-[15px] text-gray-700 font-semibold mb-2">
              Organization
            </Typography>
            <Autocomplete
              options={organizationListData}
              getOptionLabel={(option) => option["organization_name"] || ""}
              value={organization}
              onChange={(e, newValue) => setOrganization(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  size="small"
                  placeholder="Select Organization"
                  className="bg-[#FEF4ED] rounded-md"
                />
              )}
            />
          </div>

          <Field
            label="Default Archiving Days"
            value={formValues.archivingDays}
            onChange={(v) => handleChange("archivingDays", v)}
            disabled={isDisabled}
            placeholder="Enter Default Archiving Days e.g. 30"
          />

          <Field
            label="Default Deletion Days"
            value={formValues.deletionDays}
            onChange={(v) => handleChange("deletionDays", v)}
            disabled={isDisabled}
            placeholder="Enter Default Deletion Days e.g. 60"
          />

          <Field
            label="Max Users per Organization"
            value={formValues.maxUsers}
            onChange={(v) => handleChange("maxUsers", v)}
            disabled={isDisabled}
            placeholder="Enter Max Users per Organization e.g. 100"
          />

          <Field
            label="Max Active Projects"
            value={formValues.maxProjects}
            onChange={(v) => handleChange("maxProjects", v)}
            disabled={isDisabled}
            placeholder="Enter Max Active Projects e.g. 20"
          />

          <Field
            label="Max Project Docket Size"
            value={formValues.docketSize}
            onChange={(v) => handleChange("docketSize", v)}
            disabled={isDisabled}
            placeholder="Enter Max Project Docket Size e.g. 500"
          />

          <div>
            <Typography className="text-[15px] text-gray-700 font-semibold mb-2">
              Docket Size Unit (KB, MB, GB)
            </Typography>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              value={formValues.docketUnit}
              onChange={(e) => handleChange("docketUnit", e.target.value)}
              disabled={isDisabled}
              className="bg-[#FEF4ED] rounded-md"
              placeholder="Enter Docket Size Unit e.g. MB"
            />
          </div>

          {/* Time Picker */}
          <div>
            <Typography className="text-[15px] text-gray-700 font-semibold mb-2">
              Scheduled Evaluation Time
            </Typography>
            <TimePicker
              ampm
              value={
                formValues.scheduledTime
                  ? dayjs(formValues.scheduledTime, "hh:mm A")
                  : null
              }
              onChange={(newValue) => {
                handleChange(
                  "scheduledTime",
                  dayjs(newValue).format("hh:mm A")
                );
              }}
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  className: "bg-[#FEF4ED] rounded-md",
                  placeholder: "Enter  Scheduled Evaluation Time e.g. 10:00 AM",
                },
              }}
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-center mt-8 mb-4 gap-4">
          <button
            type="button"
            className="border border-[#F06D1A] text-[#F06D1A] px-4 py-2 rounded-md font-semibold"
          >
            Cancel
          </button>
          <CustomButton text="Save" isLoading={false} onClick={handleSave} />
        </div>
      </Box>
    </LocalizationProvider>
  );
};

// Reusable field with placeholder
const Field = ({ label, value, onChange, disabled, placeholder }) => (
  <div>
    <Typography className="text-[15px] text-gray-700 font-semibold mb-2">
      {label}
    </Typography>
    <TextField
      fullWidth
      variant="outlined"
      size="small"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className="bg-[#FEF4ED] rounded-md"
      placeholder={placeholder || "Enter value"}

    />
  </div>
);

export default OrganisationCard;
