export const tailwindStyles = {
  buttonPrimary:
    "w-full mt-2 py-3 font-semibold text-white bg-gradient-to-r from-orange-500 to-red-500 rounded-lg shadow-md hover:from-red-500 hover:to-orange-500 transition-all",
  formContainer: "absolute top-1/2 right-25 transform -translate-y-1/2 bg-white p-8 rounded-lg shadow-lg w-130",
  inputField: "w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500",
  otpInput: "w-10 h-10 text-center text-lg border border-gray-400 rounded-md mx-1",
  sidebarName: "bg-gradient-to-r from-orange-600 to-orange-300 text-white shadow-[1px_2px_3px_2px_lightgrey] transition-all",
  CardButton: "bg-gradient-to-r from-orange-600 to-orange-300 text-white  transition-all px-4 py-2 rounded-md hover:bg-orange-600 font-bold font-[Open Sans] w-32",
  tableHeaderName: 'font-bold font-[Open Sans] text-[22px]',
  closeIcon: 'bg-red-100 text-red-500 hover:bg-red-200 rounded-full p-2 w-8 h-8 flex items-center justify-center cursor-pointer',
  disabledCloseIcon: 'bg-gray-100 text-gray-500 hover:bg-gray-200 rounded-full p-2 w-8 h-8 flex items-center justify-center',
  allFilterButton: 'col-span-3 border border-gray-300 px-4 py-3 flex items-center justify-between space-x-2 shadow-sm rounded-md cursor-pointer text-[#000000]',
  searchiconDiv: 'col-span-9 border border-gray-300 px-3 py-3 flex items-center shadow-sm rounded-md',
  tableContainerBorder: 'mt-4 border border-gray-300 rounded-md shadow-sm h-200 overflow-y-auto',
  tablePageHead: 'border border-gray-300 shadow-md rounded-md p-4 mt-4',
  approveButton: "bg-[#E8F8F3] text-[#10B981] hover:bg-[#C6F6E2] rounded-full p-2 w-8 h-8 flex items-center justify-center",
  rejectButton: "bg-[#FFECEB] text-[#FF4136] hover:bg-[#FFC4C1] rounded-full p-2 w-8 h-8 flex items-center justify-center",
  cellStyle: "   font-[Open Sans]  text-[#475569]  text-[16px]",
  profileButton: "flex items-center gap-2 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer",
  orgCancelButtun:"border border-orange-400 text-orange-500 px-4 py-2 rounded-md font-medium hover:bg-orange-50 font-bold cursor-pointer",
  orgApproveButton:"bg-gradient-to-r from-orange-400 to-orange-500 font-bold text-white px-5 py-2 rounded-md shadow hover:opacity-90 cursor-pointer",
};
