export interface Field {
  id: number|string;
  // type: "text" | "checkbox" | "select" | "button" | "radio" | "date" | "section" | "upload" | "toggle" | "autocomplete";
  type: 0| 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  label: string;
  placeholder?: string;
  name: string;
  required: boolean;
  filter?: boolean;
  options?: string[];
  fields?: Field[];
  columnSize?: number;
  textfieldtype?:string;
  min?:number;
  max?: number;
  size?: number;
  position?: number;
  section_id?: string;
  dropdown_type?:string; 
  input_type?:string; 
  dateFormat?: "DD/MM/YYYY" | "MM/DD/YYYY" | "YYYY/MM/DD";
  // type: "field" | "Section"; // Section is a valid type in your response
  // field_type?: "text" | "checkbox" | "radio" | "select" | "date" | "toggle" | "upload" | "button" | "autocomplete"; 
}

export type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}

export interface FormField {
  id: number;
  type: FieldType;
  label: string;
  placeholder?: string;
  name?: string;
  required: boolean;
  options?: FormOption[];
  size?: number;
  position?: number;
  section_id?: string | number;
  textfieldtype?: string;
  min?: number;
  max?: number;
  filter?: boolean;
  dateFormat?: string;
  field_type?: string;
  mask?: string;
  multiple?: boolean;
  accept?: string;
  fullWidth?: boolean;
  onClick?: (values: Record<string, unknown>) => void;
  startIcon?: React.ElementType;
  endIcon?: React.ElementType;
}

export interface FormFieldWithValue extends FormField {
  value: string | number | boolean | File | File[] | string[] | null;
}


export interface FormValues {
  fields: FormFieldWithValue[];
}

export interface FormErrors {
  [key: string]: string | null;
}

export interface FormSection {
  id: string | number;
  label: string;
  position?: number;
}

export interface FormConfig {
  sections: FormSection[];
  fields: FormField[];
}
