import type React from "react";
import {
	Checkbox,
	FormControlLabel,
	Box,
	IconButton,
	Button,
	Typography,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import CustomTextField from "../ui/textfield";
import { useState } from "react";
type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}
interface FormValues {
	[key: string]: string | number | boolean | File | File[] | null | undefined;
  }
interface Field {
	id: string | number;
	type: FieldType;
	label: string;
	placeholder?: string;
	name?: string;
	required: boolean;
	filter?: boolean;
	options?: FormOption[];
	size?: number;
	position?: number;
	section_id?: string | number;
	textfieldtype?: string;
	min?: number;
	max?: number;
	dateFormat?: string;
	mask?: string;
	dropdown_type?: string;
	multiple?: boolean;
	accept?: string;
	fullWidth?: boolean;
	onClick?: (values: FormValues) => void;
	startIcon?: React.ElementType;
	endIcon?: React.ElementType;
  }
interface RulesTabProps {
	selectedField: Field;
	setSelectedField: React.Dispatch<React.SetStateAction<Field | null>>;
	validationErrors?: { [key: string]: string[] };
}

const RulesTab: React.FC<RulesTabProps> = ({
	selectedField,
	setSelectedField,
}) => {
	
	const [optionErrors, setOptionErrors] = useState<Record<number, boolean>>({});

	const handleOptionChange = (index: number, value: string) => {
		const updatedOptions = [...(selectedField.options || [])];
		if (typeof updatedOptions[0] === "object" && updatedOptions[0] !== null) {
			updatedOptions[index] = { ...updatedOptions[index], value };
		} else {
			updatedOptions[index] = { id: Date.now(), value };
		}

		setSelectedField({
			...selectedField,
			options: updatedOptions,
		});

		setOptionErrors((prev) => ({
			...prev,
			[index]: value.trim() === "", 
		}));
	};

	const handleAddOption = () => {
		const hasEmpty = (selectedField.options || []).some((opt, i) => {
			const value = getOptionValue(opt);
			if (!value || value.trim() === "") {
				setOptionErrors((prev) => ({ ...prev, [i]: true }));
				return true;
			}
			return false;
		});

		if (hasEmpty) return;

		const newOption =
			selectedField.type === 3 ? { id: Date.now(), value: "" } : "";

		setSelectedField({
			...selectedField,
			options: [...(selectedField.options || []), newOption as FormOption],
		});
	};

	const handleDeleteOption = (index: number) => {
		const updatedOptions = [...(selectedField.options || [])];
		updatedOptions.splice(index, 1);
		setSelectedField({
			...selectedField,
			options: updatedOptions,
		});
	};

	const getOptionValue = (option: string | { value: string }) =>
		typeof option === "object" ? option.value : option;

	return (
		<Box className="space-y-4">
			<FormControlLabel
				control={
					<Checkbox
						sx={{
							"& .MuiSvgIcon-root": {
								fill: "#F06D1A",
							},
						}}
						checked={selectedField.required}
						onChange={(e) =>
							setSelectedField({
								...selectedField,
								required: e.target.checked,
							})
						}
					/>
				}
				label="Required"
			/>
			<FormControlLabel
				control={
					<Checkbox
						sx={{
							"& .MuiSvgIcon-root": {
								fill: "#F06D1A",
							},
						}}
						checked={selectedField.filter}
						onChange={(e) =>
							setSelectedField({
								...selectedField,
								filter: e.target.checked,
							})
						}
					/>
				}
				label="Filter Applicable"
			/>

			{selectedField.type === 1 && (
				<CustomTextField
					label="Text Field Type"
					select
					value={selectedField.textfieldtype || "text"}
					onChange={(e) =>
						setSelectedField({
							...selectedField,
							textfieldtype: e.target.value,
						})
					}
					options={[
						{ value: "number", label: "Number" },
						{ value: "text", label: "Text" },
					]}
				/>
			)}

			{(selectedField.type === 9|| selectedField.type === 3) && (
				<>
				{selectedField.type === 3 && (
					<CustomTextField
						label="Dropdown Type"
						select
						value={selectedField.dropdown_type || "single-select"}
						onChange={(e) =>
							setSelectedField({
								...selectedField,
								dropdown_type: e.target.value,
							})
						}
						options={[
							{ value: "single-select", label: "Single-select" },
							{ value: "multi-select", label: "Multi-select" },
						]}
					/>)}

					<Typography variant="subtitle1">Add Options</Typography>
					{selectedField.options?.map((option, index) => (
						<Box key={option.id} display="flex" alignItems="center" mb={1}>
							<CustomTextField
								label=""
								value={getOptionValue(option)}
								onChange={(e) => handleOptionChange(index, e.target.value)}
								error={optionErrors[index] || false}
								helperText={optionErrors[index] ? "Option cannot be empty" : ""}
							/>
							<IconButton onClick={() => handleDeleteOption(index)}>
								<DeleteIcon />
							</IconButton>
						</Box>
					))}
					<Button
						onClick={handleAddOption}
						size="small"
						variant="contained"
						className="sm:w-auto bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case"
					>
						+Add
					</Button>
				</>
			)}

			{selectedField.textfieldtype === "number" && (
				<>
					<CustomTextField
						label="Min Value"
						value={selectedField.min ?? ""}
						onChange={(e) =>
							setSelectedField({
								...selectedField,
								min: e.target.value ? Number(e.target.value) : undefined,
							})
						}
					/>
					<CustomTextField
						label="Max Value"
						value={selectedField.max ?? ""}
						onChange={(e) =>
							setSelectedField({
								...selectedField,
								max: e.target.value ? Number(e.target.value) : undefined,
							})
						}
					/>
				</>
			)}
			{selectedField.type === 2 && (
				<>
					<Typography variant="subtitle1">Checkbox Options</Typography>
					{selectedField.options?.map((option, index) => (
						<Box key={option.id} display="flex" alignItems="center" mb={1}>
							<CustomTextField
								label=""
								value={getOptionValue(option)}
								onChange={(e) => handleOptionChange(index, e.target.value)}
								error={optionErrors[index] || false}
								helperText={optionErrors[index] ? "Option cannot be empty" : ""}
							/>
							<IconButton onClick={() => handleDeleteOption(index)}>
								<DeleteIcon />
							</IconButton>
						</Box>
					))}
					<Button
						onClick={handleAddOption}
						size="small"
						variant="contained"
						className="sm:w-auto bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case"
					>
						+ Add
					</Button>
				</>
			)}
			{selectedField.type === 5 && (
				<CustomTextField
					label="Date Format"
					select
					value={selectedField.dateFormat || "DD/MM/YYYY"}
					onChange={(e) =>
						setSelectedField({
							...selectedField,
							dateFormat: e.target.value as
								| "DD/MM/YYYY"
								| "MM/DD/YYYY"
								| "YYYY/MM/DD",
						})
					}
					options={[
						{ value: "DD/MM/YYYY", label: "DD/MM/YYYY" },
						{ value: "MM/DD/YYYY", label: "MM/DD/YYYY" },
						{ value: "YYYY/MM/DD", label: "YYYY/MM/DD" },
					]}
				/>
			)}

			{selectedField.type === 6 && (
				<>
					<Typography variant="subtitle1">Radio Button Options</Typography>
					{selectedField.options?.map((option, index) => (
						<Box key={option.id} display="flex" alignItems="center" mb={1}>
							<CustomTextField
								label=""
								value={getOptionValue(option)}
								onChange={(e) => handleOptionChange(index, e.target.value)}
								error={optionErrors[index] || false}
								helperText={optionErrors[index] ? "Option cannot be empty" : ""}
							/>
							<IconButton onClick={() => handleDeleteOption(index)}>
								<DeleteIcon />
							</IconButton>
						</Box>
					))}
					<Button
						onClick={handleAddOption}
						size="small"
						variant="contained"
						className="sm:w-auto bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case"
					>
						+ Add
					</Button>
				</>
			)}
		</Box>
	);
};

export default RulesTab;
