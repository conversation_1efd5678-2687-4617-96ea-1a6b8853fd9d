"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, TextField, Typography } from "@mui/material";
import CustomButton from "../ui/Button";
import {
  addGlobalSetting,
  UpdateGlobalSetting,
  getGlobalSetting,
} from "../../services/profileApiService";
import { toast } from "react-toastify";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs from "dayjs";

const GlobalCard = () => {
  const [formData, setFormData] = useState({
    default_deletion_days: "",
    default_archiving_days: "",
    max_active_projects: "",
    max_users_per_organization: "",
    max_project_docket_size: "",
    max_project_docket_size_unit: "",
    scheduled_evaluation_time: "",
    id: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSettingsAdded, setIsSettingsAdded] = useState(false);

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const res = await getGlobalSetting();
        // console.log("Global settings response:", res.data.id);
        if (res?.data) {
          setFormData({
            default_deletion_days: res.data.default_deletion_days?.toString() || "",
            default_archiving_days: res.data.default_archiving_days?.toString() || "",
            max_active_projects: res.data.max_active_projects?.toString() || "",
            max_users_per_organization: res.data.max_users_per_organization?.toString() || "",
            max_project_docket_size: res.data.max_project_docket_size?.toString() || "",
            max_project_docket_size_unit: res.data.max_project_docket_size_unit || "",
            scheduled_evaluation_time: res.data.scheduled_evaluation_time || "",
            id: res.data.id,
          });
          setIsSettingsAdded(true);
        }
      } catch (err) {
        console.error("Failed to fetch global settings:", err);
      }
    };

    loadSettings();
  }, []);

  const handleSave = async () => {
    const payload = {
      ...formData,
      default_deletion_days: Number(formData.default_deletion_days),
      default_archiving_days: Number(formData.default_archiving_days),
      max_active_projects: Number(formData.max_active_projects),
      max_users_per_organization: Number(formData.max_users_per_organization),
      max_project_docket_size: Number(formData.max_project_docket_size),
      id: formData.id,
    };

    try {
      setIsLoading(true);

      if (!isSettingsAdded) {
        await addGlobalSetting(payload);
        setIsSettingsAdded(true);
        toast.success("Global settings added successfully.");
      } else {
        await UpdateGlobalSetting(payload);
        toast.success("Global settings updated successfully.");
      }
    } catch (error) {
      toast.error("Failed to save global settings.");
      console.error("Error saving settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className="w-full max-w-5xl mx-auto bg-white rounded-lg border border-[#E2E8F0]">
        <div className="text-lg text-gray-700 font-semibold border-b border-[#E2E8F0] px-6 py-4">
          Global Settings
        </div>

        <div className="grid grid-cols-2 gap-6 px-6 py-2">
          <Field
            label="Default Deletion Days"
            value={formData.default_deletion_days}
            onChange={(val) => handleChange("default_deletion_days", val)}
            placeholder="Enter Default Deletion Days e.g. 30"
          />

          <Field
            label="Default Archiving Days"
            value={formData.default_archiving_days}
            onChange={(val) => handleChange("default_archiving_days", val)}
            placeholder="Enter Default Archiving Days e.g. 60"
          />

          <Field
            label="Max Active Projects"
            value={formData.max_active_projects}
            onChange={(val) => handleChange("max_active_projects", val)}
            placeholder="Enter Max Active Projects e.g. 100"
          />

          <Field
            label="Max Users per Organization"
            value={formData.max_users_per_organization}
            onChange={(val) => handleChange("max_users_per_organization", val)}
            placeholder="Enter Max Users per Organization e.g. 50"
          />

          <Field
            label="Max Project Docket Size (by Model Type numbers)"
            value={formData.max_project_docket_size}
            onChange={(val) => handleChange("max_project_docket_size", val)}
            placeholder="Enter Max Project Docket Size e.g. 500"
          />

          <TextFieldBlock
            label="Max Project Docket Size Unit (MB, KB or GB)"
            value={formData.max_project_docket_size_unit}
            onChange={(e) =>
              handleChange("max_project_docket_size_unit", e.target.value)
            }
            placeholder="Enter Max Project Docket Size Unit e.g. MB"
          />

          <div>
            <div className="mb-3">
              <Typography className="text-[15px] text-gray-700 font-semibold font-['Open_Sans']">
                Scheduled Evaluation Time
              </Typography>
            </div>
            <TimePicker
              ampm
              value={
                formData.scheduled_evaluation_time
                  ? dayjs(formData.scheduled_evaluation_time, "hh:mm A")
                  : null
              }
              onChange={(newValue) => {
                handleChange(
                  "scheduled_evaluation_time",
                  dayjs(newValue).format("hh:mm A")
                );
              }}
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  className: "bg-[#FEF4ED] rounded-md",
                },
              }}
            />
          </div>
        </div>

        <div className="flex justify-center mt-8 mb-4 gap-4">
          <button
            type="button"
            className="border border-[#F06D1A] text-[#F06D1A] px-4 py-2 rounded-md font-semibold"
          >
            Cancel
          </button>
          <CustomButton text="Save" isLoading={isLoading} onClick={handleSave} />
        </div>
      </Box>
    </LocalizationProvider>
  );
};

const Field = ({
  label,
  value,
  onChange,
  placeholder,
}: {
  label: string;
  value: string;
  onChange: (val: string) => void;
  placeholder?: string;
}) => (
  <div>
    <div className="mb-3">
      <Typography className="text-[15px] text-gray-700 font-semibold font-['Open_Sans']">
        {label}
      </Typography>
    </div>
    <TextField
      fullWidth
      type="number"
      placeholder={placeholder || "Enter number"}
      variant="outlined"
      size="small"
      value={value}
      onChange={(e) => onChange(e.target.value.replace(/\D/g, ""))}
      className="bg-[#FEF4ED] rounded-md"
    />
  </div>
);

const TextFieldBlock = ({
  label,
  value,
  onChange,
  endAdornment,
  placeholder,
}: {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  endAdornment?: React.ReactNode;
  placeholder?: string;
}) => (
  <div>
    <div className="mb-3">
      <Typography className="text-[15px] text-gray-700 font-semibold font-['Open_Sans']">
        {label}
      </Typography>
    </div>
    <TextField
      fullWidth
      variant="outlined"
      size="small"
      value={value}
      onChange={onChange}
      className="bg-[#FEF4ED] rounded-md"
      placeholder={placeholder || "Enter value"}
      InputProps={{
        endAdornment: endAdornment,
      }}
    />
  </div>
);

export default GlobalCard;
