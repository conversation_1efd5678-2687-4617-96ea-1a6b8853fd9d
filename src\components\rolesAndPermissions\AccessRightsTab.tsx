// components/AccessRightsTab.tsx
import type React from "react";
import {
  Box, FormControl, Select, MenuItem, Typography, Table, TableBody, TableCell, TableContainer,
  TableHead, TableRow, Paper, Checkbox, Button
} from "@mui/material";
import type { Role } from "../../components/interface/rolesInterface";

interface AccessRightsTabProps {
  selectedRole: string;
  setSelectedRole: (role: string) => void;
  rolesListData: Role[];
  permissionAccess: { id: string; name: string }[];
  accessRights: { id: string; name: string; [key: string]: boolean | string }[];
  handleCheckboxChange: (index: number, field: string) => void;
  handleSavePermission: () => void;
}

const AccessRightsTab: React.FC<AccessRightsTabProps> = ({
  selectedRole,
  setSelectedRole,
  rolesListData,
  permissionAccess,
  accessRights,
  handleCheckboxChange,
  handleSavePermission
}) => (
  <>
    <Box className="px-6 py-4">
      <FormControl fullWidth sx={{ maxWidth: 300 }}>
        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: "8px" }}>
          Select Role
        </Typography>
        <Select
          value={selectedRole}
          onChange={(e) => setSelectedRole(e.target.value)}
          displayEmpty
          sx={{
            backgroundColor: "#FEF4ED",
            borderRadius: "8px",
            height: "42px"
          }}
        >
          <MenuItem value="select" disabled>
            Select Role
          </MenuItem>
          {rolesListData.map((role) => (
            <MenuItem key={role.id} value={role.name}>
              {role.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>

    <TableContainer component={Paper} className="shadow-none rounded-lg px-4 py-2 flex-grow mt-4">
      <Table sx={{ width: "90%", margin: "auto", border: "1px solid #d1d5db" }}>
        <TableHead>
          <TableRow>
            <TableCell>Module</TableCell>
            {permissionAccess.map((action: { id: string; name: string }) => (
              <TableCell key={action.id} className="text-capitalize">
                {action.name}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {accessRights.map((right: { id: string; name: string; [key: string]: boolean | string }, index) => (
            <TableRow key={right.id} className={index % 2 === 1 ? "bg-[#FEF4ED]" : ""}>
              <TableCell>{right.name}</TableCell>
              {["create", "read", "update", "delete"].map((field) => (
                <TableCell key={field}>
                  <Checkbox
                    checked={!!right[field]}
                    onChange={() => handleCheckboxChange(index, field)}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Box className="flex justify-center gap-4 mt-6 px-6 pb-4">
        <Button
          variant="outlined"
          sx={{ borderColor: "#F97316", color: "#F97316" }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSavePermission}
          sx={{ backgroundColor: "#F97316", textTransform: "none" }}
        >
          Save
        </Button>
      </Box>
    </TableContainer>
  </>
);

export default AccessRightsTab;
