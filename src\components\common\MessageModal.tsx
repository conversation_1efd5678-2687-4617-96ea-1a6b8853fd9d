"use client";

import React, { useEffect, useState } from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    Typography,
    Box,
    Rating,
    Icon<PERSON>utton,
    Divider,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { getRatingList } from "../../services/desApiService";
import { formatDate } from "../../constants/fieldtype";
import Loader from "../loader/loader";

interface MessageModalProps {
    open: boolean;
    onClose: () => void;
    datasetId: string;
}

interface Message {
    name: string;
    rating: number;
    created_at: string;
    comm_data: string;
}

const MessageModal: React.FC<MessageModalProps> = ({ open, onClose, datasetId }) => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
        if (!datasetId) return;

        const fetchRatings = async () => {
            setLoading(true);
            try {
                const response = await getRatingList({ interactionId: datasetId });
                setMessages(response.data || []);
            } catch (error) {
                console.error("Failed to fetch rating data", error);
                setMessages([]);
            } finally {
                setLoading(false);
            }
        };

        fetchRatings();
    }, [datasetId]);

    return (
        <>
        {loading && <Loader />}
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <DialogTitle
                sx={{
                    borderBottom: '1px solid #D1D5DB',
                    fontWeight: 'bold',
                    marginBottom: '16px',
                }}
            >
                Rating Details
                <IconButton
                    onClick={onClose}
                    sx={{ position: "absolute", right: 16, top: 16 }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>

            <DialogContent sx={{ maxHeight: "500px", overflowY: "auto" }}>
                {loading ? (
                    <Typography>Loading...</Typography>
                ) : messages?.length === 0 ? (
                    <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                        <Typography variant="body1" color="text.secondary">
                            Data not found
                        </Typography>
                    </Box>
                ) : (
                    messages.map((msg, index) => (
                        <Box key={index} mb={3}>
                            <Typography variant="h6" sx={{ fontWeight: "bold" }}>{msg.name}</Typography>
                            <Rating
                                value={msg.rating}
                                precision={0.5}
                                readOnly
                                sx={{
                                    color: "#FE8231",
                                    "& .MuiRating-iconFilled": {
                                        color: "#FE8231",
                                    },
                                    "& .MuiRating-iconHover": {
                                        color: "#FE8231",
                                    },
                                }}
                            />
                            <Typography variant="body2" color="text.secondary">
                                {formatDate(msg.created_at)}
                            </Typography>
                            <Typography variant="body1" mt={1}>
                                {msg.comm_data}
                            </Typography>
                            {index < messages.length - 1 && <Divider sx={{ my: 2 }} />}
                        </Box>
                    ))
                )}
            </DialogContent>
        </Dialog>
        </>
    );
};

export default MessageModal;
