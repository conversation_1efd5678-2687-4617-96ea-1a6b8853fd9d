"use client";

import React from 'react'
import { Box, MenuItem, TextField, Typography } from '@mui/material'
import CustomButton from '../ui/Button';
import AvatarFallback from '../ui/AvatarFallback';

const PersonalCard = () => {
  return (
    <Box className="w-full max-w-5xl mx-auto bg-white rounded-lg border border-[#E2E8F0] ">
      {/* Profile Image and Button */}
      <div className='text-lg text-gray-700 font-semibold border-b border-[#E2E8F0] px-6 py-4'>
        Profile
      </div>
      <div className="flex items-center gap-6 border-b border-[#E2E8F0] px-6 py-6 mb-6">
        <AvatarFallback name="Alexa Rawles" className="w-8 h-8 rounded-full border-2 border-gray-300" />
        <div>
          <h3 className="text-lg font-semibold text-gray-800 pb-2"><PERSON><PERSON></h3>
          <CustomButton
            text="Upload New Picture"
            isLoading={false}
          />
        </div>
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 px-6 py-2">
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              Full Name
            </Typography>
          </div>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          />
        </div>
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              User Name
            </Typography>
          </div>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          />
        </div>
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              Email
            </Typography>
          </div>
          <TextField
            fullWidth
            placeholder="<EMAIL>"
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          />
        </div>
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              Role
            </Typography>
          </div>
          <TextField
            fullWidth
            select
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          >
            <MenuItem value="admin">Admin</MenuItem>
            <MenuItem value="editor">Editor</MenuItem>
          </TextField>
        </div>
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              Organization Name
            </Typography>
          </div>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          />
        </div>
        <div>
          <div className="mb-3">
            <Typography className="text-[15px] font-semibold font-['Open_Sans'] ">
              Country
            </Typography>
          </div>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            className="bg-[#FEF4ED] rounded-md"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center mt-8 mb-4 gap-4">
        <button className="border border-[#F06D1A] text-[#F06D1A] px-4 py-2 rounded-md font-semibold">
          Cancel
        </button>
        <CustomButton
          text="Save"
          isLoading={false}
        />
      </div>
    </Box>
  )
}

export default PersonalCard;