"use client";

import React from "react";
import { useRouter } from "next/navigation";
type Activity = {
    name: string;
    status: "Created" | "Released For Evaluation" | "ready for review";
};

interface RecentActivityTableProps {
    activities: Activity[];
}

const RecentActivityTable: React.FC<RecentActivityTableProps> = ({ activities }) => {
    const router = useRouter();
    const statusBadge = (status: Activity["status"]) => {
        const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
        if (status === "Created") {
            return (
                <span className={`bg-gray-100 text-gray-800 ${baseClasses}`}>
                    Created
                </span>
            );
        }
        return (
            <span className={`bg-blue-100 text-[#0074D9] font-[700] ${baseClasses}`}>
                Released For Evaluation
            </span>
        );
    };

    return (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex justify-between items-center pb-4 mb-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
                <a href="#" className="text-sm text-blue-600 hover:underline font-medium">
                    View All
                </a>
            </div>

            <table className="w-full text-sm table-auto">
                <thead>
                    <tr className="text-left text-lg border-b border-gray-200">
                        <th className="pb-3 px-4 font-bold text-[#000000]">Project Dockets</th>
                        <th className="pb-3 px-4 font-bold text-[#000000]">Status</th>
                        <th className="pb-3 px-4"></th>
                    </tr>
                </thead>
                <tbody>
                    {activities.slice(0, 5).map((row, i) => (
                        <tr key={i} className="border-b border-gray-200 last:border-b-0">
                            <td className="py-3 px-4 text-gray-900 font-semibold">
                                {row.name}
                            </td>
                            <td className="py-3 px-4">{statusBadge(row.status)}</td>
                            <td className="py-3 px-4 text-right">
                                {row.status === "ready for review" && (
                                    <button
                                        type="button"
                                        className="bg-gradient-to-r from-orange-600 to-orange-300 text-white rounded-md px-4 py-1.5 text-sm font-bold transition-all duration-300 hover:from-orange-700 hover:to-orange-400"
                                        onClick={() => router.push("/project-dockets/view")}
                                    >
                                        View Results
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default RecentActivityTable;
