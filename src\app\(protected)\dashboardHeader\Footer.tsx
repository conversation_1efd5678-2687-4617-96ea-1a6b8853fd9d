import { Box, Typography, useTheme, useMediaQuery } from "@mui/material";

const Footer: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      component="footer"
      sx={{
        // py: isMobile ? 1.5 : 1.5,
        px: isMobile ? 2 : 3,
        textAlign: "center",
        backgroundColor: "#fff",
        boxShadow: "inset 0 1px 4px rgba(0,0,0,0.05)",
        width: "100%",
        position: "relative",
        bottom: 0,
        left: 0,
        right: 0,
        [theme.breakpoints.up('md')]: {
          px: 4,
          // py: 1.5,
        }
      }}
    >
      <Typography 
        variant={isMobile ? "body2" : "body1"} 
        color="text.secondary"
        sx={{
          fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            md: '0.875rem'
          },
          lineHeight: 1.5,
          padding: {
            xs: '0.5rem 0',
            sm: '0.5rem 0',
            md: '0.5rem 0'
          }
        }}
      >
        © AI Community Portal. All rights reserved.
      </Typography>
    </Box>
  );
};

export default Footer;