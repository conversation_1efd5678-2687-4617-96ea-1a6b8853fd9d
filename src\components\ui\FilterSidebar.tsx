"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Typography, Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { desFormGet } from "../../services/desApiService";
import CircularProgress from "@mui/material/CircularProgress";
import { toast } from "react-toastify";
import type { SelectChangeEvent } from "@mui/material";
import type { FormField, FormFieldWithValue } from "../formBuilder/types";
import "./style.css";
import FilterFieldRender from "../forms/searchFields";

const FilterSidebar: React.FC<{
  type?: number;
  open: boolean;
  onClose: () => void;
  onFilterApply?: (filteredFields: { fields: string; value: string }[]) => void;
  initialFilters?: { fields: string; value: string }[];
}> = ({ open, onClose, type, onFilterApply, initialFilters = [], }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [formConfig, setFormConfig] = useState<{ fields: FormField[] }>({
    fields: [],
  });

  useEffect(() => {
    if (!open) return;

    const fetchFormData = async () => {
      setLoading(true);
      try {
        const desformvalue = await desFormGet({ type: type });

        const fieldsWithValues = desformvalue.data.fields.map(field => {
          const matchingFilter = initialFilters.find(f => f.fields === field.label);
          return {
            ...field,
            value: matchingFilter
              ? field.multiple
                ? matchingFilter.value.split(',')
                : matchingFilter.value
              : field.multiple
                ? []
                : ''
          };
        });

        setFormConfig({
          ...desformvalue.data,
          fields: fieldsWithValues
        });
      } catch (err) {
        console.error("Failed to fetch form data", err);
      } finally {
        setLoading(false);
      }
    };

    fetchFormData();
  }, [type, open, initialFilters]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormConfig((prevConfig: { fields: FormField[] }) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field: FormField) => {
        if (field.id.toString() === name) {
          return {
            ...field,
            value: type === "checkbox" || type === "switch" ? checked : value
          };
        }
        return field;
      })
    }));
  };

  const handleSelectChange = (
    e: SelectChangeEvent<string | string[]>,
    field: FormField
  ) => {
    const { value } = e.target;
    setFormConfig((prevConfig: { fields: FormField[] }) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((f: FormField) => {
        if (f.id === field.id) {
          return {
            ...f,
            value: value
          };
        }
        return f;
      })
    }));
  };

  const handleClear = () => {
    setFormConfig((prevConfig: { fields: FormField[] }) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field: FormField) => ({
        ...field,
        value: field.multiple ? [] : ''
      }))
    }));
    onClose();
    if (typeof onFilterApply === 'function') {
      onFilterApply([]);
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const filteredFields = formConfig.fields
      .filter((item) => {
        if (!item.filter) return false;
        const value = (item as FormFieldWithValue).value;
        if (Array.isArray(value)) return value.length > 0;
        return value !== '' && value !== undefined && value !== null;
      })
      .map((item) => {
        const value = (item as FormFieldWithValue).value;

        let formattedValue: string;

        if (Array.isArray(value)) {
          if (value.length > 0 && typeof value[0] === 'string') {
            formattedValue = value.join(',');
          } else if (value[0] instanceof File) {
            formattedValue = (value as File[]).map(file => file.name).join(', ');
          } else {
            formattedValue = '';
          }
        } else {
          formattedValue = String(value);
        }

        return {
          fields: item.label,
          value: formattedValue
        };
      });

    try {
      onClose();
      if (typeof onFilterApply === 'function') {
        onFilterApply(filteredFields);
      }
    } catch (error) {
      toast.error("Filter application failed", error);
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;

  return (
    <div
      className="fixed top-0 right-0 h-full w-[350px] bg-white shadow-lg z-50"
      style={{
        zIndex: 1000,
      }}
    >
      <Box className="flex justify-between items-center p-2 pl-4 pr-4 border-b border-gray-200">
        <Typography variant="h6" className="font-bold text-[#000000]">
          Filter
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>

      {loading && (
        <div className="flex items-center justify-center h-full">
          <CircularProgress size={80} style={{ color: "#F45C24" }} />
        </div>
      )}

      <Card sx={{
        overflow: 'auto',
        height: 'calc(100% - 120px)',
        padding: 2,
      }}>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4">
            {formConfig.fields
              .filter((item: FormField) => item.filter === true && (item.label !== "Dataset Name" && item.label !== "Project Name" && item.label !== "Organization Name"))
              .map((field: FormField) => (
                <div key={field.id}>
                  <FilterFieldRender
                    field={field as FormFieldWithValue}
                    onChange={handleChange}
                    onSelectChange={(e) => handleSelectChange(e, field)}
                    fileformat={"jpeg, jpg, png, docx, pdf"}
                  />
                </div>
              ))}
          </div>
          <Box className="flex justify-between p-4 border-t absolute bottom-0 left-0 right-0 bg-white border-t-gray-200">
            <Button
              variant="outlined"
              className="text-transform-none"
              onClick={handleClear}
              style={{
                // padding: "10px",
                width: "48%",
                fontSize: "15px",
                color: "#F45C24",
                borderColor: "#F45C24",
              }}
            >
              Clear
            </Button>
            <Button
              className="text-transform-none"
              type="submit"
              variant="contained"
              style={{
                // padding: "10px",
                width: "48%",
                fontSize: "15px",
                background: "linear-gradient(to right, #F45C24, #FFCB80)",
              }}
            >
              Apply
            </Button>
          </Box>
        </form>
      </Card>
    </div>
  );
};

export default FilterSidebar;
