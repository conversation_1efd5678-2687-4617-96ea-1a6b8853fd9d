"use client";
import { useEffect, useState, useMemo } from "react";
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>po<PERSON>,
	Divide<PERSON>,
	Box,
	Icon<PERSON><PERSON><PERSON>,
	Drawer,
	InputLabel,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import "./style.css";
import FormFieldRenderer from "../../../../components/forms/renderFields";
import type {
	FormConfig,
	FormField,
	FormSection,
} from "../../../../components/interface/formInterface";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import Loader from "../../../../components/loader/loader";
// import {
// 	dataSetFileUpload,
// 	dataSetFileDelete,
// } from "../../../../services/dataSetapiService";
import type { SelectChangeEvent } from "@mui/material";
import {
	desFormGet,
	organizationList,
	registerCreate,
} from "../../../../services/desApiService";
import AddIcon from "@mui/icons-material/Add";
import { toast } from "react-toastify";
import UploadDrawerForm from "../../datasets/create/uploadeDrawer";
import RequestModal from "../../../../components/projectDocket/RequestModal";
import { docketUpload } from "../../../../services/uploadApiService";

export default function ProjectDocketForm() {
	const router = useRouter();
	const [formConfig, setFormConfig] = useState<FormConfig>({
		sections: [],
		fields: [],
		type: 0,
	});
	const [, setSubmitSuccess] = useState(false);
	const [loading, setLoading] = useState(false);
	const [open, setOpen] = useState(false);
	const [modelOpen, setModelOpen] = useState(false);
	const [formStatus, setFormStatus] = useState<number | null>(null);
	type DatasetValue = {
		id: string;
		"Dataset Name": string;
	};
	const [datasetValues, setDatasetValues] = useState<DatasetValue[]>([]);
	const [uploadedFiles, setUploadedFiles] = useState<
		Record<string, { file_path: string; id: string }>
	>({});
	const [drawerContinued, setDrawerContinued] = useState(false);

	const handleUploadedFile = (data: { file_path: string; id: string }) => {
		setUploadedFiles((prev) => ({
			...prev,
			upload_file: data,
		}));

		const fileUploadField = formConfig.fields.find((field) => field.type === 7);
		if (fileUploadField) {
			formik.setFieldValue(fileUploadField.id.toString(), data);
		}
	};

	useEffect(() => {
		const fetchFormData = async () => {
			setLoading(true);
			try {
				const desformvalue = (await desFormGet({
					type: 3,
				})) as { data: FormConfig };
				setFormConfig(desformvalue.data);
				const datasetFormvalue = await organizationList({
					page: 1,
					pageSize: 100,
					type: 2,
				});
				setDatasetValues(datasetFormvalue?.data?.formdtoData || []);
			} catch (err) {
				console.error("Failed to fetch form data", err);
			} finally {
				setLoading(false);
			}
		};
		fetchFormData();
	}, []);

	const handleOpenDrawer = () => {
		setOpen(true);
		setDrawerContinued(false);
	};

	const handleCloseDrawer = () => {
		setOpen(false);
	};

	const { initialValues, validationSchema } = useMemo(() => {
		const initialVals: Record<
			string,
			string | number | boolean | File | File[] | null
		> = {};
		const validationShape: Record<string, Yup.AnySchema> = {};

		for (const field of formConfig.fields) {
			initialVals[field.id] = "";

			if (field.required) {
				switch (field.type) {
					case 1:
						if (field.textfieldtype === "number") {
							let schema = Yup.number()
								.typeError("Must be a valid number")
								.required(`${field.label} is required`);

							if (field.min !== undefined) {
								schema = schema.min(field.min, `Minimum value is ${field.min}`);
							}
							if (field.max !== undefined) {
								schema = schema.max(field.max, `Maximum value is ${field.max}`);
							}
							validationShape[field.id] = schema;
						} else if (
							field.label.includes("email") ||
							field.label.includes("Email")
						) {
							validationShape[field.id] = Yup.string()
								.matches(
									/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
									"Must be a valid email address",
								)
								.required(`${field.label} is required`);
						} else if (
							field.label.includes("Phone Number") ||
							field.label.includes("phone number")
						) {
							validationShape[field.id] = Yup.string()
								.matches(
									/^[+]?[0-9]{1,4}?[-.\s\(\)]?(\(?[0-9]{1,3}?\)?[-.\s]?)?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,9}$/,
									"Must be a valid phone number",
								)
								.required(`${field.label} is required`);
						} else {
							validationShape[field.id] = Yup.string().required(
								`${field.label} is required`,
							);
						}
						break;

					case 3:
						validationShape[field.id] = Yup.mixed().required(
							`${field.label} is required`,
						);
						break;
					case 7:
						validationShape[field.id] = Yup.mixed()
							.required("File upload is required")
							.test(
								"fileType",
								"Only .pkl, .joblib, .pth, .h5, or .onnx files are allowed",
								(value) => {
									if (!value) return false;
									if (typeof value === "object" && "file_path" in value) {
										const ext =
											typeof value.file_path === "string"
												? value.file_path.split(".").pop()?.toLowerCase()
												: undefined;
										return ["pkl", "joblib", "pth", "h5", "onnx"].includes(
											ext || "",
										);
									}
									if (value instanceof File) {
										const ext = value.name.split(".").pop()?.toLowerCase();
										return ["pkl", "joblib", "pth", "h5", "onnx"].includes(
											ext || "",
										);
									}
									return false;
								},
							)
							.test("fileSize", "File size must be ≤ 5MB", (value) => {
								if (!value) return false;
								if (typeof value === "object" && "file_path" in value) {
									return true;
								}
								if (value instanceof File) {
									return value.size <= 5 * 1024 * 1024;
								}
								return false;
							});
						break;

					default:
						validationShape[field.id] = Yup.string().required(
							`${field.label} is required`,
						);
				}
			}
		}

		return {
			initialValues: initialVals,
			validationSchema: Yup.object(validationShape),
		};
	}, [formConfig.fields]);

	const formik = useFormik({
		enableReinitialize: true,
		initialValues,
		validationSchema,
		onSubmit: async (values) => {
			try {
				const payload = {
					sections: formConfig.sections,
					fields: formConfig.fields.map((field) => {
						const value = values[field.id];
						if (
							field.type === 7 &&
							typeof value === "object" &&
							value !== null &&
							"file_path" in value
						) {
							return { ...field, value };
						}
						return { ...field, value };
					}),
					type: formConfig.type,
					status: formStatus ?? 4,
				};

				await registerCreate(payload);
				toast.success("Project Dockets Created successfully");
				setSubmitSuccess(true);
				router.push("/project-dockets");
			} catch (error) {
				console.error("Registration failed:", error);
			}
		},
	});

	const handleSelectChange = (
		e: SelectChangeEvent<string | string[]>,
		field: FormField,
	) => {
		formik.setFieldValue(field.id.toString(), e.target.value);
	};

	const handleChange = async (
		e:
			| React.ChangeEvent<HTMLInputElement>
			| {
					target: {
						name: string;
						value: string | number | boolean | File | File[];
						type?: string;
						checked?: boolean;
						files?: FileList;
					};
			  },
	) => {
		const { name, value, type, checked, files } = e.target as HTMLInputElement;

		if (files && files.length > 0) {
			try {
				const formData = new FormData();
				formData.append("uploadFile", files[0]);

				const uploadRes = await docketUpload(formData);
				const fullPath = uploadRes?.data?.file_path || files[0].name;
				const fileId = uploadRes?.data?.id;
				const filePath = fullPath?.replace(/^Documents\//, "");

				const fileObj = { file_path: filePath, id: fileId };

				formik.setFieldValue(name, fileObj);
				setUploadedFiles((prev) => ({
					...prev,
					[name]: fileObj,
				}));
				setDrawerContinued(true);
			} catch (error) {
				console.error("File upload failed:", error);
				formik.setFieldError(name, "File upload failed");
			}
		} else if (type === "checkbox" || type === "switch") {
			formik.setFieldValue(name, checked);
		} else {
			formik.setFieldValue(name, value);
		}
	};

	const handleFileDelete = async (fieldId: string) => {
		const fileData = uploadedFiles[fieldId];
		if (!fileData?.file_path) return;
	
		// Simply remove the file from state without calling the API
		setUploadedFiles((prev) => {
			const updated = { ...prev };
			delete updated[fieldId];
			return updated;
		});
	
		const fileField = formConfig.fields.find(
			(field) => field.id.toString() === fieldId,
		);
		if (fileField) {
			formik.setFieldValue(fieldId, "");
		}
		setDrawerContinued(false);
	};

	const handleDataset = () => {
		router.push("/project-dockets");
	};

	return (
		<>
			{loading && <Loader />}
			<div className="min-h-screen flex items-center justify-center p-4 relative d-flex">
				<div className="w-full max-w-5xl">
					<Box className="flex justify-between items-center mb-6 p-4 rounded-t-lg z-50 sticky top-0 sticky backdrop-blur-sm">
						<Typography variant="h5" className="font-bold text-[#000000]">
							Create Project Dockets
						</Typography>
						<Button
							style={{ background: "white", color: "black" }}
							variant="contained"
							startIcon={<ArrowBackIcon />}
							onClick={handleDataset}
						>
							Back
						</Button>
					</Box>

					<Card className="w-full max-w-5xl backdrop-blur-sm bg-white/90 overflow-auto shadow-xl hover:shadow-4xl transition-shadow duration-300">
						<form onSubmit={formik.handleSubmit} noValidate>
							{formConfig.sections
								.sort((a, b) => (a.position || 0) - (b.position || 0))
								.map((section: FormSection) => (
									<div key={section.id} className="mb-4">
										<Typography variant="h6" className="mb-4 p-4">
											{section.label}
										</Typography>
										<Divider className="mb-4" />
										<div className="grid pt-4 pl-8 pr-8 grid-cols-1 md:grid-cols-2 gap-4">
											{formConfig.fields
												.filter(
													(field: FormField) => field.section_id === section.id,
												)
												.sort((a, b) => (a.position || 0) - (b.position || 0))
												.map((field: FormField) => (
													<div key={field.id}>
														{field.type !== 7 ? (
															<FormFieldRenderer
																field={{
																	...field,
																	value: formik.values[field.id] as
																		| string
																		| number
																		| boolean
																		| File
																		| File[]
																		| null,
																}}
																error={
																	formik.touched[field.id] &&
																	formik.errors[field.id]
																		? formik.errors[field.id]
																		: ""
																}
																onChange={handleChange}
																onSelectChange={(e) =>
																	handleSelectChange(
																		e as SelectChangeEvent<string | string[]>,
																		field,
																	)
																}
																setModelOpen={setModelOpen}
																options={
																	field.type === 9 &&
																	field.label === "Tagging to sample datasets"
																		? datasetValues
																				.filter((ds) => ds["Dataset Name"])
																				.map((ds) => ({
																					id: ds.id,
																					value:
																						ds["Dataset Name"] ||
																						"Unnamed Dataset",
																				}))
																		: field.options
																}
															/>
														) : (
															<Box>
																{uploadedFiles.upload_file &&
																drawerContinued ? (
																	<Box
																		sx={{
																			display: "flex",
																			alignItems: "center",
																			justifyContent: "space-between",
																			border: "1px solid #ddd",
																			borderRadius: "4px",
																			padding: "8px 16px",
																			backgroundColor: "#fafafa",
																		}}
																	>
																		<Typography variant="body1">
																			{uploadedFiles.upload_file.file_path
																				.length > 30
																				? `${uploadedFiles.upload_file.file_path.slice(0, 30)}...`
																				: uploadedFiles.upload_file.file_path}
																		</Typography>
																		<Button
																			onClick={() =>
																				handleFileDelete("upload_file")
																			}
																			color="error"
																			size="small"
																		>
																			Remove
																		</Button>
																	</Box>
																) : (
																	<>
																	<InputLabel sx={{paddingBottom:"7px"}}>Upload Project Docket <span className="text-red-500 ml-1">*</span></InputLabel>
																	<Box
																		className="upload-button uploaded-button"
																		onClick={handleOpenDrawer}
																	>
																		<Typography
																			variant="body1"
																			sx={{ color: "text.secondary" }}
																		>
																			Upload Project Docket
																		</Typography>
																		<IconButton size="small" color="default">
																			<AddIcon />
																		</IconButton>
																	</Box>
																	</>
																)}
																{formik.touched[field.id] &&
																	formik.errors[field.id] && (
																		<Typography
																			color="error"
																			variant="body2"
																			sx={{ mt: 1 }}
																		>
																			{formik.errors[field.id] as string}
																		</Typography>
																	)}
															</Box>
														)}
													</div>
												))}
										</div>
									</div>
								))}
							<Box className="flex flex-col sm:flex-row items-center justify-center gap-4 p-5">
								<button
									type="button"
									onClick={() => {
										setFormStatus(3);
										formik.handleSubmit();
									}}
									style={{
										padding: "9px",
										width: "200px",
										fontSize: "15px",
									}}
									className="w-full sm:w-auto border border-[#F06D1A] text-[#F06D1A] px-4 py-2 rounded-md font-semibold text-sm sm:text-base text-transform-none"
								>
									Save as Draft
								</button>

								<Button
									type="submit"
									onClick={() => setFormStatus(4)}
									variant="contained"
									style={{
										padding: "9px",
										width: "200px",
										fontSize: "15px",
										
									}}
									className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r font-bold from-[#F45C24] to-[#FFCB80] text-white text-sm sm:text-base text-transform-none"
								>
									Submit
								</Button>
							</Box>
						</form>
					</Card>
				</div>
			</div>

			<Drawer
				anchor="right"
				open={open}
				onClose={handleCloseDrawer}
				PaperProps={{
					sx: {
						width: "700px",
						display: "flex",
						flexDirection: "column",
						justifyContent: "space-between",
					},
				}}
			>
				<UploadDrawerForm
					setOpen={setOpen}
					onFileUpload={handleUploadedFile}
					onContinue={() => setDrawerContinued(true)}
				/>
			</Drawer>
			<RequestModal open={modelOpen} onClose={() => setModelOpen(false)} />
		</>
	);
}
