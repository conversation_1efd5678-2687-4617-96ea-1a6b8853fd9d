import { createApiClient } from '../lib/createApiClient';

const REQUEST_BASE_URL = process.env.NEXT_PUBLIC_REQUEST_BASE_URL;
const requestMgmApi = createApiClient(REQUEST_BASE_URL);

export const getDropdownRequest = async () => {
	const response = await requestMgmApi.get('/request-types');
	return response;
};

export const getRequestList = async (payload: { org_id: string; page: number; limit: number; search?: string; request_type?: number; status?: number; type?: string; }) => {
	const response = await requestMgmApi.get("/org", {
		params: payload
	});
	return response;
};

export const creatRequest = async (payload) => {
	const response = await requestMgmApi.post('/', payload);
	return response;
};

export const UpdateRequest = async (payload) => {
	const response = await requestMgmApi.put(`/status?id=${payload.id}`, payload);
	return response;
};
