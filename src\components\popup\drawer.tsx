"use client";

import type React from "react";
import {
  Drawer,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import type { DrawerProps } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

interface CommonDrawerProps extends DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children?: React.ReactNode;
  width?: number | string;
}

const SideDrawer: React.FC<CommonDrawerProps> = ({
  isOpen,
  onClose,
  title = "Drawer",
  children,
  width = { xs: "100%", md: 600, lg: 800 },
  ...rest
}) => {
  return (
    <Drawer
      anchor="right"
      open={isOpen}
      onClose={onClose}
      PaperProps={{
        sx: { width },
        className: "bg-white shadow-lg",
      }}
      {...rest}
    >
      {/* Drawer Header */}
      <Box className="flex items-center justify-between p-4 border-b border-gray-200">
        <Typography
          variant="h6"
          sx={{ fontWeight: "bold", fontFamily: "Open Sans" }}
        >
          {title}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      {/* Drawer Body */}
      <Box className="p-4 overflow-y-auto h-[calc(100vh-70px)]">
        {children}
      </Box>
    </Drawer>
  );
};

export default SideDrawer;
