import type React from "react";
import {  <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import type { Field } from "./types";

interface SidebarProps {
  addField: (type: Field["type"]) => void;
  addSection: (type: Field["type"]) => void;
}

const fieldTypeLabels: Record<Field["type"], string> = {
  0: "Add Section",
  1: "Add Text",
  2: "Add Checkbox",
  3: "Add Select",
  4: "Add Button",
  5: "Add Date",
  6: "Add Radio",
  7: "Add File Upload",
  8: "Add Toggle",
  9: "Add AutoComplete",
};

const Sidebar: React.FC<SidebarProps> = ({ addField, addSection }) => {
  const handleFieldSelection = (type: Field["type"]) => {
    if (type === 0) {
      addSection(type);
    } else {
      addField(type);
    }
  };

  return (
    <Box
      className="p-4 overflow-auto"
      sx={{
        height: "85vh",
        borderRight: "4px solid #F06D1A",
        pr: 3, 
        "&::-webkit-scrollbar": { width: "6px" },
        "&::-webkit-scrollbar-thumb": {
          background: "#FF6B6B",
          borderRadius: "8px",
        },
      }}
    >
      <Typography
        className=" pb-3 pr-3"
        sx={{ fontSize: "18px",  color: "#333" }}
      >
        Dynamic Fields List
      </Typography>
      
      {([0, 1, 2, 3, 4, 5, 6, 7, 8, 9] as Array<Field["type"]>).map((type) => (
        <Button
          key={type}
          fullWidth
          variant="outlined"
          onClick={() => handleFieldSelection(type)}
          sx={{
            mb: 1,
            borderRadius: "10px",
            color: "black",
            borderColor: "#b1b0b0",
            fontWeight: "500",
            fontSize: "14px",
            py: 1.2,
            transition: "0.3s",
            "&:hover": {
              backgroundColor: "#F06D1A",
              color: "white",
              borderColor: "#FF6B6B",
            },
          }}
        >
          {fieldTypeLabels[type]}
        </Button>
      ))}
    </Box>
  );
};

export default Sidebar;
