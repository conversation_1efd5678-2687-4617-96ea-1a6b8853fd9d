
import React from "react";
import { Card, Card<PERSON>ontent, ClickAwayListener, Typography } from "@mui/material";

interface NotificationItem {
  id: number;
  text: string;
  time: string;
  color: string;
  initials: string;
}

interface Props {
  count: number;
  isOpen: boolean;
  onClose: () => void;
}

const notifications: NotificationItem[] = [
  {
    id: 1,
    text: "ORG1-12345 is ready to be approved",
    time: "10 Mins ago",
    color: "#2ECC40", 
    initials: "PD",
  },
  {
    id: 2,
    text: "ORG1-12345 is getting archived",
    time: "10 Hrs ago",
    color: "#B10DC9", 
    initials: "PD",
  },
  {
    id: 3,
    text: "ORG1-12345 is set for deletion",
    time: "10 mins ago",
    color: "#FF4136", 
    initials: "PD",
  },
];

const NotificationPopup: React.FC<Props> = ({ count, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <ClickAwayListener onClickAway={onClose}>
      <Card className="absolute right-0 mt-2  w-[280px] rounded-lg shadow-lg"
        sx={{ border: "1px solid #E2E8F0" }}>
        <Typography className="text-sm font-medium text-gray-800 border-b border-[#E2E8F0] px-4 py-2">
          Notifications ({count})
        </Typography>

        <CardContent className="space-y-3 " sx={{ paddingBottom: "13px !important" }}>
          {notifications.map((item) => (
            <div key={item.id} className="flex items-start gap-3">
              <div
                className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center text-white text-sm font-semibold"
                style={{ backgroundColor: item.color }}
              >
                {item.initials}
              </div>
              <Typography className="flex-1">
                <div className="w-[220px] text-gray-800 text-sm font-medium truncate">
                  {item.text}
                </div>

                <div className="text-xs text-gray-500">{item.time}</div>
              </Typography>
            </div>
          ))}
        </CardContent>
      </Card>
    </ClickAwayListener>
  );
};

export default NotificationPopup;

