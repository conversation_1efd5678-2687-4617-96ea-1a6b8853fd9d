"use client";

import type React from "react";
import {
  Drawer,
  Checkbox,
  FormControlLabel,
  Box,
  Divider,
  Typography,
} from "@mui/material";

interface Header {
  id: string;
  name: string;
}

interface HeaderSelectionDrawerProps {
  open: boolean;
  onClose: () => void;
  optionalHeaders: Header[];
  selectedFields: string[];
  setSelectedFields: (fields: string[]) => void;
}

const HeaderSelectionDrawer: React.FC<HeaderSelectionDrawerProps> = ({
  open,
  onClose,
  optionalHeaders,
  selectedFields,
  setSelectedFields,
}) => {
  const handleToggleField = (id: string) => {
    if (selectedFields.includes(id)) {
      setSelectedFields(selectedFields.filter((fieldId) => fieldId !== id));
    } else {
      setSelectedFields([...selectedFields, id]);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFields(optionalHeaders.map((header) => header.id));
    } else {
      setSelectedFields([]);
    }
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: "80%", sm: 300, md: 350 }, p: 2 },
      }}
    >
      <Box className="flex flex-col h-full">
        {/* Header */}
        <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>
          Manage Headers
        </Typography>

        {/* Select All */}
        <Box className="flex items-center justify-between mb-2">
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedFields.length === optionalHeaders.length}
                indeterminate={
                  selectedFields.length > 0 &&
                  selectedFields.length < optionalHeaders.length
                }
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
            }
            label="Select All"
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Headers List */}
        <Box className="flex flex-col gap-2 overflow-y-auto">
          {optionalHeaders.map((header) => (
            <FormControlLabel
              key={header.id}
              control={
                <Checkbox
                  checked={selectedFields.includes(header.id)}
                  onChange={() => handleToggleField(header.id)}
                />
              }
              label={
                <Typography variant="body2" sx={{ fontSize: 15 }}>
                  {header.name}
                </Typography>
              }
            />
          ))}
        </Box>
      </Box>
    </Drawer>
  );
};

export default HeaderSelectionDrawer;
