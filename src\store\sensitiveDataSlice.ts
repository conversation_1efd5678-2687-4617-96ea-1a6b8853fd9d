import { createSlice, type PayloadAction } from '@reduxjs/toolkit';


interface SensitiveDataState {
  encryptedData: string | null;
  decryptedData: Record<string, unknown> | null;
  loading: boolean;
  error: string | null;
}

const initialState: SensitiveDataState = {
  encryptedData: null,
  decryptedData: null,
  loading: false,
  error: null,
};

const sensitiveDataSlice = createSlice({
  name: 'sensitiveData',
  initialState,
  reducers: {
    // Action to store encrypted data
    storeEncryptedData(state, action: PayloadAction<string>) {
      state.encryptedData = action.payload;
      state.decryptedData = null;
    },
    
    // Action to set decrypted data (temporary in memory)
    setDecryptedData(state, action: PayloadAction<Record<string, unknown>>) {
      state.decryptedData = action.payload;
    },
    
    // Action to clear sensitive data from memory
    clearSensitiveData(state) {
      state.encryptedData = null;
      state.decryptedData = null;
    },
    
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
  },
});

// Thunk to fetch sensitive data
// Import AppDispatch from your Redux store setup
import type { AppDispatch } from './store';

export const fetchSensitiveData = () => async (dispatch: AppDispatch) => {
  try {
    dispatch(setLoading(true));
    dispatch(setError(null));
    
    // const response = await api.get(`/sensitive-data/${userId}`, {
    //   headers: {
    //     'X-Data-Classification': 'Restricted',
    //   },
    // });
    
    // Store encrypted data in Redux
    // dispatch(storeEncryptedData(response.data.encryptedPayload));
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch sensitive data';
    dispatch(setError(errorMessage));
  } finally {
    dispatch(setLoading(false));
  }
};

// Thunk to decrypt data (only when needed)
export const decryptSensitiveData = () => async (dispatch: AppDispatch) => {
  try {
    // const { encryptedData } = getState().sensitiveData;
    
    // if (!encryptedData) {
    //   throw new Error('No encrypted data available');
    // }
    
    dispatch(setLoading(true));
    dispatch(setError(null));
    
    // Decrypt the data (this happens in memory, not persisted)
    // const decrypted = await decryptData(encryptedData, passphrase);
    
    // Validate decrypted data structure
    // if (!decrypted || typeof decrypted !== 'object') {
    //   throw new Error('Invalid decrypted data structure');
    // }
    
    // // Store decrypted data temporarily in Redux
    // dispatch(setDecryptedData(decrypted));
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Decryption failed';
    dispatch(setError(errorMessage));
    // Clear any partial decrypted data on error
    dispatch(setDecryptedData(null));
  } finally {
    dispatch(setLoading(false));
  }
};

export const { 
  storeEncryptedData, 
  setDecryptedData, 
  clearSensitiveData, 
  setLoading, 
  setError 
} = sensitiveDataSlice.actions;

export default sensitiveDataSlice.reducer;