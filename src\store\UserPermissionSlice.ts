// store/accessRightsSlice.ts

import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface Permission {
  permission_id: string;
  permission_name: string;
}

interface Module {
  module_id: string;
  module_name: string;
  permissions: Permission[];
}

export interface AccessRight {
  role_id: string;
  role_name: string;
  modules: Module[];
}

interface AccessRightsState {
  accessRightsList: AccessRight; // Changed from AccessRight[]
  loading: boolean;
}

const initialState: AccessRightsState = {
  accessRightsList: {
    role_id: "",
    role_name: "",
    modules: [],
  },
  loading: false,
};

const accessRightsSlice = createSlice({
  name: "accessRights",
  initialState,
  reducers: {
    setAccessRightsList: (state, action: PayloadAction<AccessRight>) => {
      state.accessRightsList = action.payload;
    },
    clearAccessRightsList: (state) => {
      state.accessRightsList = {
        role_id: "",
        role_name: "",
        modules: [],
      };
    },
  },
});

export const { setAccessRightsList, clearAccessRightsList } = accessRightsSlice.actions;
export default accessRightsSlice.reducer;
