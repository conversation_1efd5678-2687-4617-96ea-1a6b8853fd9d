"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Modal,
  Typography,
  Button,
  TextField,
  IconButton,
  CircularProgress,
} from "@mui/material";
import Rating from "@mui/material/Rating";
import CloseIcon from "@mui/icons-material/Close";
import { addRating } from "../../services/desApiService";
import { toast } from "react-toastify";

interface RatingModalProps {
  open: boolean;
  onClose: () => void;
  name: string;
  datasetId: string;
  datasetApi: () => Promise<void>; // Add this line
}

const RatingModal: React.FC<RatingModalProps> = ({ open, onClose, name, datasetId, datasetApi }) => {
  const [rating, setRating] = useState<number | null>(0);
  const [feedback, setFeedback] = useState<string>("");
  const [loading, setLoading] = useState(false);

  // Reset state when modal opens
  useEffect(() => {
    if (open) {
      setRating(0);
      setFeedback("");
    }
  }, [open]);

  const handleClose = () => {
    setRating(0);
    setFeedback("");
    onClose();
  };

  const handleSubmit = async () => {
    if (rating === null) return;

    setLoading(true);

    const payload = {
      interaction_id: datasetId,
      rating,
      message: feedback,
    };

    try {
      await addRating(payload);
      toast.success("Thank you! Your rating has been submitted.");
      datasetApi(); // Refresh the dataset list
      handleClose(); // Reset and close the dialog/modal
    } catch (error) {
      console.error("Failed to submit rating:", error);
      const errorMessage =
        error?.response?.data?.message || "Oops! Something went wrong. Please try again.";

      if (errorMessage.toLowerCase().includes("duplicate interaction")) {
        toast.error("You already rated this Project Docket.");
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };


  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[350px] sm:w-[450px] bg-white shadow-md rounded-lg p-6"
        sx={{
          outline: "none",
          border: "none",
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        }}
      >
        <Box>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6" fontWeight="bold" className="text-[#000000]">
              Rate the {name}!
            </Typography>
            <IconButton onClick={handleClose}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Box mt={2} mb={2} borderBottom="1px solid #E0E0E0" />
        </Box>

        <Typography className="text-gray-600 mt-2 text-[10px]">
          We’re listening! Let us know how helpful the AI Model was by rating it here.
        </Typography>

        <Box className="w-full my-4">
          <Rating
            value={rating}
            precision={1}
            size="large"
            style={{ color: "#FA6F15" }}
            onChange={(_, newValue) => setRating(newValue)}
          />
        </Box>

        <Typography className="text-gray-700 font-medium mb-1 text-[14px] mb-3">
          Can you tell us more?
        </Typography>
        <TextField
          placeholder="Add feedback"
          multiline
          rows={3}
          fullWidth
          variant="outlined"
          value={feedback}
          onChange={(e) => setFeedback(e.target.value)}
        />

        <Box className="flex justify-center mt-4 gap-3">
          <Button
            className="text-transform-none"
            variant="outlined"
            onClick={handleClose}
            disabled={loading}
            sx={{

              width: "100%",
              color: "#FA6F15",
              borderColor: "#FA6F15",
            }}
          >
            Cancel
          </Button>
          <Button
            className="text-transform-none"
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
            sx={{
              backgroundColor: "#FA6F15",

              width: "100%",
              fontWeight: "bold",
            }}
          >
            {loading ? <CircularProgress size={20} color="inherit" /> : "Submit"}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default RatingModal;
