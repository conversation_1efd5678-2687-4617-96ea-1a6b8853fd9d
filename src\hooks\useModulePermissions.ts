// hooks/useModulePermissions.ts
import { useMemo } from "react";
import { useAppSelector } from "../store/store";

export interface ModulePermissions {
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export function useModulePermissions(moduleName: string): ModulePermissions {
  interface Permission {
    permission_name: string;
  }

  interface Module {
    module_name: string;
    permissions: Permission[];
  }

  interface RootState {
    accessRights?: {
      accessRightsList?: {
        modules?: Module[];
      };
    };
  }

  const accessRights = useAppSelector((state: RootState) => state?.accessRights?.accessRightsList?.modules);
  return useMemo(() => {
    const targetModule  = accessRights?.find((mod) => mod?.module_name === moduleName);
    const perms = targetModule?.permissions?.map((p) => p?.permission_name) || [];

    return {
      canCreate: perms.includes("create"),
      canRead: perms.includes("read"),
      canUpdate: perms.includes("update"),
      canDelete: perms.includes("delete"),
    };
  }, [accessRights, moduleName]);
}
