// store/datasetSlice.ts

import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface DatasetState {
  datasetList: unknown[];
  loading: boolean;
}

const initialState: DatasetState = {
  datasetList: [],
  loading: false,
};

const datasetSlice = createSlice({
  name: "dataset",
  initialState,
  reducers: {
    setDatasetList: (state, action: PayloadAction<unknown[]>) => {
      state.datasetList = action.payload;
    },
    setDatasetLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    clearDatasetList: (state) => {
      state.datasetList = [];
    },
  },
});

export const { setDatasetList, setDatasetLoading, clearDatasetList } = datasetSlice.actions;
export default datasetSlice.reducer;
