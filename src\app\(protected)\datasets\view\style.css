.css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
  }
  .css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
    color: #ababab !important;
  }
  .css-z8nmqa-MuiSvgIcon-root {
    fill: #F06D1A !important;
  }
  .css-1bz1rr0-MuiSvgIcon-root {
  fill:#F06D1A !important;
  }
  .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
    background-color: #F06D1A !important;
  }
  .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked {
    color: #F06D1A !important;
  }
  .css-1dune0f-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
  }
  /* .css-1umw9bq-MuiSvgIcon-root {
    fill:#F06D1A !important;
  } */
  .css-16bevx5-MuiFormControl-root-MuiTextField-root{
    margin-top: 10px !important;
    margin-bottom: 0px !important;
  }
  .css-16mfwdg-MuiFormControl-root{
    margin-top: 5px !important;
    margin-bottom: 0px !important;
  }
  .select-lable{
    color: #696666 !important;
  }
  
  .MuiOutlinedInput-root:not(.Mui-focused):hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23) !important; /* Default border color */
  }