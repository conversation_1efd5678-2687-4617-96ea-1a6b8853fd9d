const config = {
  darkMode: false,
    content: [
      "./app/**/*.{js,ts,jsx,tsx}",
      "./pages/**/*.{js,ts,jsx,tsx}",
      "./components/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
       fontFamily: {
        sans: ['"Open Sans"', "sans-serif"],
        "open-sans": ['"Open Sans"', "sans-serif"],
        "geist-sans": ["var(--font-geist-sans)", "sans-serif"],
        "geist-mono": ["var(--font-geist-mono)", "monospace"],
      },
        colors: {
          customBorder: "#7C8CA1",
        },
        spacing: {
          64: "16rem",
          16: "4rem",
        },
      },
    },
    plugins: [],
  };
  
  export default config;
  