import type React from "react";
import { Box } from "@mui/material";
// import type { Field } from "./types";
import CustomTextField from "../ui/textfield";
type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}
interface FormValues {
  [key: string]: string | number | boolean | File | File[] | null | undefined;
  }
interface Field {
  id: string | number;
  type: FieldType;
  label: string;
  placeholder?: string;
  name?: string;
  required: boolean;
  options?: FormOption[];
  size?: number;
  position?: number;
  section_id?: string | number;
  textfieldtype?: string;
  min?: number;
  max?: number;
  dateFormat?: string;
  mask?: string;
  dropdown_type?: string;
  multiple?: boolean;
  accept?: string;
  fullWidth?: boolean;
  onClick?: (values: FormValues) => void;
  startIcon?: React.ElementType;
  endIcon?: React.ElementType;
  }
interface BasicDetailsTabProps {
  selectedField: Field;
  setSelectedField: React.Dispatch<React.SetStateAction<Field | null>>;
}

const BasicDetailsTab: React.FC<BasicDetailsTabProps> = ({
  selectedField,
  setSelectedField,
}) => {
  return (
    <Box className="space-y-4 d-flex">
      <CustomTextField
        label="Label"
        value={selectedField.label}
        onChange={(e) =>
          setSelectedField({ ...selectedField, label: e.target.value })
        }
      />
      <CustomTextField
        label="Placeholder"
        value={selectedField.placeholder}
        onChange={(e) =>
          setSelectedField({ ...selectedField, placeholder: e.target.value })
        }
      />
      <CustomTextField
        label="Custom Size"
        select
        value={selectedField.size || 12}
        onChange={(e) =>
          setSelectedField({
            ...selectedField,
            size: Number(e.target.value),
          })
        }
        options={[
          { value: 4, label: "4" },
          { value: 6, label: "6" },
          { value: 12, label: "12" },
        ]}
      />
    </Box>
  );
};

export default BasicDetailsTab;
