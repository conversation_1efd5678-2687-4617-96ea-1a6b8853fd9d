import { Box, MenuItem, Select, Button, Typography } from '@mui/material';

const FilterPanel = () => {
    return (
        <Box className="bg-white rounded-xl border border-gray-300 shadow-lg p-4 space-y-4">
            {[
                'Project Status',
                'Organization',
                'Speciality',
                'Sub-Speciality',
                'Type of Model',
                'User Status',
            ].map((label) => (
                <div key={label}>
                    <Typography className="text-sm font-semibold text-gray-700 mb-1">
                        {label}
                    </Typography>
                    <Select
                        fullWidth
                        displayEmpty
                        defaultValue=""
                        size="small"
                        className="bg-[#FEF4ED] rounded-md"
                    >
                        <MenuItem value="">All</MenuItem>
                        {/* Add real options here */}
                    </Select>
                </div>
            ))}

            <Button
                variant="contained"
                className="w-full bg-gradient-to-r from-orange-600 to-orange-300 text-white  font-bold"
                sx={{
                    textTransform: 'none',
                    height: '40px',
                    borderRadius: '8px',
                    fontFamily: 'Open Sans',
                    fontWeight: 'bold',
                }}
            >
                Apply Filters
            </Button>
            
        </Box>
    );
};

export default FilterPanel;
