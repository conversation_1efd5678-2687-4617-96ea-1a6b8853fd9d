"use client";
import type React from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Ta<PERSON>,
	Tab,
} from "@mui/material";
// import type { Field } from "./types";
import {  useState } from "react";
import BasicDetailsTab from "./BasicDetailsTab";
import RulesTab from "./RulesTab";
type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}
interface FormValues {
	[key: string]: string | number | boolean | File | File[] | null | undefined;
  }
interface Field {
	id: string | number;
	type: FieldType;
	label: string;
	placeholder?: string;
	name?: string;
	required: boolean;
	options?: FormOption[];
	size?: number;
	position?: number;
	section_id?: string | number;
	textfieldtype?: string;
	min?: number;
	max?: number;
	dateFormat?: string;
	mask?: string;
	dropdown_type?: string;
	multiple?: boolean;
	accept?: string;
	fullWidth?: boolean;
	onClick?: (values: FormValues) => void;
	startIcon?: React.ElementType;
	endIcon?: React.ElementType;
  }
interface FieldEditorProps {
	open: boolean;
	selectedField: Field | null;
	setSelectedField: React.Dispatch<React.SetStateAction<Field | null>>;
	handleSave: () => void;
}

const FieldEditor: React.FC<FieldEditorProps> = ({
	open,
	selectedField,
	setSelectedField,
	handleSave,
}) => {
	const [activeTab, setActiveTab] = useState(0);
	const [validationErrors, setValidationErrors] = useState<{
		[key: string]: string[];
	}>({});
    const handleClose = () => {
		setSelectedField(null);
		setValidationErrors({});
		setActiveTab(0);
	}
	if (!selectedField) return null;

	return (
		<Dialog
			open={open}
			onClose={handleClose }
			fullWidth
			maxWidth="sm"
		>
			<DialogTitle>Edit Field</DialogTitle>
			<Tabs
				value={activeTab}
				onChange={(event, newValue) => setActiveTab(newValue)}
				variant="fullWidth"
			>
				<Tab label="Basic Details" />
				<Tab label="Rules" />
			</Tabs>
			<DialogContent>
				{activeTab === 0 && (
					<BasicDetailsTab
						selectedField={selectedField}
						setSelectedField={setSelectedField}
					/>
				)}
				{activeTab === 1 && (
					<RulesTab
						selectedField={selectedField}
						setSelectedField={setSelectedField}
						validationErrors={validationErrors}
					/>
				)}
			</DialogContent>
			<DialogActions
				className="px-6 pb-10 pt-1 flex justify-center gap-4"
				sx={{
					justifyContent: "center",
					paddingBottom: "15px",
					paddingTop: "0px",
				}}
			>
				<Button
					variant="outlined"
					onClick={handleClose}
					style={{
						width: "170px",
						fontSize: "15px",
						borderColor: "#F45C24",
						color: "#F45C24",
						
					}}
					className="sm:w-auto px-6 py-3 normal-case text-transform-none"
				>
					Cancel
				</Button>
				<Button
	variant="contained"
	onClick={() => {
		if (selectedField?.options && Array.isArray(selectedField.options)) {
			const hasEmpty = selectedField.options.some((option) => {
				const value = typeof option === "object" ? option.value : option;
				return value?.toString().trim() === "";
			});
	
			if (hasEmpty) {
				setValidationErrors({ options: ["Please fill all option values before saving."] });
			}
		}
	
		handleSave();
	}}
	style={{ width: "170px", fontSize: "15px", textTransform: "none" }}
	className="sm:w-auto px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case"
>
	Save for Changes
</Button>
			</DialogActions>
		</Dialog>
	);
};

export default FieldEditor;
