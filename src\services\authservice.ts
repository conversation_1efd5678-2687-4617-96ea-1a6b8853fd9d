import Cookie from 'js-cookie';
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_IDENTITY_API_BASE_URL;
const rawApi = axios.create({ baseURL: API_BASE_URL });

export const refreshToken = async ({ refresh_token, user_id }: { refresh_token: string; user_id: string }) => {
	const response = await rawApi.post("/refresh-token", { refresh_token, user_id });
	const token = response.data.jwtToken;
	Cookie.set('token', token, {
		expires: 1,
		secure: true,
		sameSite: 'Lax',
	});
	return response;
};
