import {
	<PERSON><PERSON>,
	<PERSON>alogC<PERSON>nt,
	TextField,
	MenuItem,
	Button,
	IconButton,
	Box,
	Typography,
	CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { registerCreate } from "../../services/desApiService";
import { toast } from "react-toastify";

// const natureOptions = ["Push for Evaluation", "Ad-hoc Request"];
const natureOptions = ["Tag dataset"];


const RequestModal = ({
	open,
	onClose,
	projectId,
	modelName,
}: {
	open: boolean;
	onClose: () => void;
	projectId: string;
	modelName: string;
}) => {
	const [formData, setFormData] = useState({
		projectName: modelName,
		natureOfRequest: "",
		message: "",
		
	});

	const [errors, setErrors] = useState({
		natureOfRequest: "",
		message: "",
	});

	const [loading, setLoading] = useState(false);

	console.log("projectId", projectId);
	console.log("modelName", modelName);

	const handleChange = (
		field: "natureOfRequest" | "message",
		value: string,
	) => {
		setFormData({ ...formData, [field]: value });
		setErrors({ ...errors, [field]: "" });
	};

	const validate = () => {
		const newErrors: typeof errors = {
			natureOfRequest: formData.natureOfRequest
				? ""
				: "Select a nature of request",
			message: formData.message ? "" : "Enter your message",
		};
		setErrors(newErrors);
		return !Object.values(newErrors).some((err) => err !== "");
	};

	const handleSubmit = async () => {
		if (!validate()) return;

		try {
			setLoading(true);
			const payload = {
				projectName: formData.projectName,
				natureOfRequest: formData.natureOfRequest,
				message: formData.message,
				type: 2,
				status: 9,
			};
			const res = await registerCreate(payload);
			if (res?.status === 200) {
				toast.success(res.data.message);
			} else {
				alert("Failed to submit request");
			}
			onClose();
		} catch (error) {
			console.error("Submission failed:", error);
			alert("An error occurred while submitting.");
		} finally {
			setLoading(false);
		}
	};
	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			maxWidth="sm"
			PaperProps={{ sx: { borderRadius: "8px" } }}
		>
			<Box className="bg-white">
				<Box className="flex justify-between items-center px-6 pt-6">
					<Typography variant="subtitle1" className="font-semibold text-black">
						Request SingHealth
					</Typography>
					<IconButton onClick={onClose} size="small">
						<CloseIcon />
					</IconButton>
				</Box>

				<DialogContent className="px-6 pt-4 pb-6 space-y-4">
					<Box>
						<Typography sx={{ marginBottom: "10px" }} variant="body2">
							Project Name
						</Typography>
						<TextField
							fullWidth
							value={formData.projectName}
							placeholder="Project Name"
							onChange={(e) =>
								setFormData({ ...formData, projectName: e.target.value })
							}
							// InputProps={{ readOnly: true }}
							variant="outlined"
							InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
						/>
					</Box>

					<Box>
						<Typography sx={{ marginBottom: "10px" }} variant="body2">
							Nature of Request
						</Typography>
						<TextField
							fullWidth
							select
							value={formData.natureOfRequest}
							onChange={(e) => handleChange("natureOfRequest", e.target.value)}
							variant="outlined"
							error={!!errors.natureOfRequest}
							SelectProps={{
								displayEmpty: true,
								sx: {
									color: "#000000",
									"& .MuiSelect-select": {
										color: "#000000"
									}
								}
							}}
							helperText={errors.natureOfRequest}
							InputProps={{ 
								style: { backgroundColor: "#FDF1E7" },
							}}
							sx={{
								"& .MuiMenuItem-root": {
									color: "#000000"
								}
							}}
						>
							<MenuItem value="" disabled>
								Select Nature of Request
							</MenuItem>
							{natureOptions.map((option) => (
								<MenuItem key={option} value={option}>
									{option}
								</MenuItem>
							))}
						</TextField>
					</Box>

					<Box>
						<Typography sx={{ marginBottom: "10px" }} variant="body2">
							Comments
						</Typography>
						<TextField
							fullWidth
							multiline
							rows={4}
							placeholder="Type your message here"
							value={formData.message}
							onChange={(e) => handleChange("message", e.target.value)}
							variant="outlined"
							error={!!errors.message}
							helperText={errors.message}
							InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
						/>
					</Box>

					<Button
						fullWidth
						onClick={handleSubmit}
						disabled={loading}
						variant="contained"
						className="text-transform-none"
						sx={{
							background: "linear-gradient(90deg, #FF6A00 0%, #FF9500 100%)",
							color: "#fff",
							fontWeight: 600,
							
							borderRadius: "5px",
							boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.15)",
							paddingY: "10px",
							"&:hover": {
								background: "linear-gradient(90deg, #e65c00 0%, #e68500 100%)",
							},
						}}
					>
						{loading ? (
							<CircularProgress size={24} color="inherit" />
						) : (
							"Submit"
						)}
					</Button>
				</DialogContent>
			</Box>
		</Dialog>
	);
};

export default RequestModal;
