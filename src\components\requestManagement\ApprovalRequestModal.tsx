import { useState } from "react";
import {
	Dialog,
	DialogTitle,
	DialogContent,
	TextField,
	MenuItem,
	Button,
	IconButton,
	InputLabel,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useAppSelector } from "../../store/store";
import type { RootState } from "../../store/store";
import type { Role } from "../interface/rolesInterface";
import { tailwindStyles } from "../../styles/tailwindStyles";
import Cookies from "js-cookie";
import { creatRequest } from "../../services/requestapiService";
import { toast } from "react-toastify";

interface ApprovalRequestModalProps {
	isOpen: boolean;
	onClose: () => void;
	dropdownList: {
		id: string;
		name: string;
	}[];
	fetchRequests?: () => void;
}
const ApprovalRequestModal: React.FC<ApprovalRequestModalProps> = ({
	isOpen,
	onClose,
	dropdownList,
	fetchRequests,
}) => {
    const initialFormData = {
        nature: "",
        value: "",
        description: "",
    };
	const [formdata, setFormData] = useState({
		nature: "",
		value: "",
		description: "",
	});
	const { nature, value, description } = formdata;
	const organization = useAppSelector(
		(state: RootState) => state.user?.userDetail?.organization,
	) as Role;

	const userId = Cookies.get("user_id");

	const handleSubmit = async () => {
		if (!nature || !value || !description) {
			alert("Please fill in all required fields.");
			return;
		}

		const requestData = {
			created_by_user_id: userId || "",
			request_type: nature,
			value: Number(value),
			commands_by_organization: description,
			org_id: organization?.id || "",
		};
		const res = await creatRequest(requestData);
		if (res?.status === 200) {
			toast.success("Request submitted successfully");
			fetchRequests();
            setFormData(initialFormData);

		}

		onClose();
	};

	return (
		<Dialog open={isOpen} onClose={onClose} fullWidth maxWidth="sm">
			<DialogTitle className="flex justify-between items-center">
				<span className="font-semibold text-lg">Request to SingHealth</span>
				<IconButton onClick={onClose} size="small">
					<CloseIcon />
				</IconButton>
			</DialogTitle>
			<DialogContent>
				<div className="flex flex-col gap-4 py-2">
					<InputLabel>Organization Name</InputLabel>
					<TextField
						fullWidth
						value={organization?.name || ""}
						disabled
						InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
					/>

					<InputLabel>Nature of Request</InputLabel>
					<TextField
						select
						fullWidth
						value={nature}
						onChange={(e) =>
							setFormData({ ...formdata, nature: e.target.value })
						}
						InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
						SelectProps={{
							displayEmpty: true,
						}}
					>
						<MenuItem value="" disabled>
							Select nature of request
						</MenuItem>
						{dropdownList.map((option) => (
							<MenuItem key={option.id} value={option.id}>
								{option.name}
							</MenuItem>
						))}
					</TextField>

					<InputLabel>Value</InputLabel>
					<TextField
						fullWidth
						placeholder="Enter your value"
						value={value}
						onChange={(e) =>
							setFormData({ ...formdata, value: e.target.value })
						}
						InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
					/>

					<InputLabel>Description</InputLabel>
					<TextField
						placeholder="Enter your request..."
						multiline
						rows={4}
						fullWidth
						value={description}
						onChange={(e) =>
							setFormData({ ...formdata, description: e.target.value })
						}
						InputProps={{ style: { backgroundColor: "#FDF1E7" } }}
					/>

					<Button
						variant="contained"
						onClick={handleSubmit}
						className={`mt-2 ${tailwindStyles.sidebarName}`}
					>
						Submit
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default ApprovalRequestModal;
