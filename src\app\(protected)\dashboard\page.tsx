'use client';

import type React from 'react';

import {
    Building2,
    <PERSON><PERSON><PERSON>,
    Users2,

} from 'lucide-react';
import DashboardCard from '../../../components/common/DashboardCard';
// import OrganizationSection from './OrganizationSection';
import RecentProjectsSection from './RecentProjectSection';
// import FilterPanel from './FilterPanel';
// import ProjectTable from './ProjectTable';
import { getDashboardData } from '../../../services/profileApiService';
import { useState, useEffect } from 'react';
import { RootState, useAppSelector } from '../../../store/store';
import { organizationList } from '../../../services/desApiService';
import Loader from '../../../components/loader/loader';
import CustomButton from '../../../components/ui/Button';

interface DashboradValues {
    users: { last_month: string; this_year: string; total_count: string };
    project: {
        last_month: string; this_year: string; total_count: string,
        active_count: string; archive_count: string; pending_count: string; rejected_count: string;
    };
    organization: { last_month: string; this_year: string; total_count: string };
}


type Organization = {
    id: string;
    name: string;
    organization_name: string;
    organization_id: string;
    email: string;
    lastActive: string;
    status: number;
    createdAt: string;
    type: string;
};
interface Project {
    projectId: string;
    modelName: string;
    Description: string
    version: string;
    type: string;
    specialty: string;
    subSpecialty: string;
    submittedOn: string;
    gpu: string;
    status: string;
    statusColor: string;
    timeCount: number;
    average_rating: number;
    time: string;
}
const OverviewPage: React.FC = () => {
    const userDetail = useAppSelector((state: RootState) => state?.user?.userDetail,);
    const [isLoading, setIsLoading] = useState(true);
    const [dashboardData, setDashboardData] = useState<DashboradValues | null>(null);
    const [projectListData, setProjectListData] = useState<Project[]>([]);
    // const [appliedFilters, setAppliedFilters] = useState<{ fields: string; value: string }[]>([]);
    // const [queryStrings, setQueryString] = useState({
    //     page: 1,
    //     pageSize: 10,
    //     type: 3,
    //     formname: '',
    // });

    const [organizationListData, setOrganizationListData] = useState<
        Organization[]
    >([]);

    console.log("Dashboard Data:", dashboardData);
    console.log("Organization List Data:", organizationListData);
    // const combinedFilters = [...appliedFilters];

    useEffect(() => {
        fetchOrganizationData();
        fetchFormData()
        fetchFormDataProject()
    }, []);
    const fetchOrganizationData = async () => {
        // setLoading(true);
        try {
            const res = await getDashboardData();
            console.log("Dashboard Data:", res.data);
            setDashboardData(res.data);
        } catch (err) {
            console.error("Failed to fetch form data:", err);
        } finally {
            // setLoading(false);
        }
    };

    const fetchFormData = async () => {
        setIsLoading(true);
        try {
            const params = {
                page: 1,
                pageSize: 10,
                type: 1,


            };
            const desformvalue = await organizationList(params);
            setOrganizationListData(desformvalue?.data?.formdtoData || []);

        } catch (err) {
            console.error("Failed to fetch form data", err);
            setOrganizationListData([]);

        } finally {
            setIsLoading(false);
        }
    };
    const fetchFormDataProject = async () => {
        // setLoading(true);
        try {
            // const params = {
            //     ...queryStrings,
            //     filter: combinedFilters.length > 0 ? JSON.stringify(combinedFilters) : undefined,
            // };
            const params = {
                page: 1,
                pageSize: 10,
                type: 2,

            };
            const response = await organizationList(params);
           
            interface FormDtoItem {
                id: string;
                "Dataset Name"?: string;
                "Description"?: string;
                "Model Type"?: string;
                Speciality?: string;
                "Sub Speciality"?: string;
                created_at?: string;
                status?: string;

                updated_at?: string;
            }

            const formattedData = response?.data?.formdtoData?.map(
                (item: FormDtoItem) => ({
                    projectId: item.id,
                    modelName: item["Dataset Name"] || "N/A",
                    Description: item["Description"] || "N/A",
                    version: item["Base Version"] || "N/A",
                    type: item["Model Type"] || "N/A",
                    gpu: item["Required CPU"] || "N/A",
                    specialty: item.Speciality || "N/A",
                    subSpecialty: item["Sub Speciality"] || "N/A",
                    submittedOn: item.updated_at
                        ? new Date(item.updated_at).toLocaleDateString("en-GB")
                        : "N/A",
                    status: item.status,
                    statusColor: "bg-gray-200 text-gray-600",
                    time: item.updated_at
                        ? new Date(item.updated_at).toLocaleTimeString("en-US", {
                            hour: "numeric",
                            minute: "2-digit",
                            hour12: true,
                        })
                        : "N/A",

                }),
            );

            setProjectListData(formattedData || []);

        } catch (err) {
            console.error("Failed to fetch form data", err);
            setProjectListData([]);
        } finally {
            // setLoading(false);
        }
    };
    const handleCreateProject = () => {
        window.open("http://*************:8080/", "_blank");
    };
    const filteredProjects = projectListData;
    const formattedRecentProjects = filteredProjects.map((project) => ({
        name: project.modelName,
        status: project.status === "Created" || project.status === "Released For Evaluation"
            ? (project.status as "Created" | "Released For Evaluation")
            : "Created",
        Speciality: project.specialty || "N/A",
        subSpecialty: project.subSpecialty || "N/A",
        Description: project.Description || "N/A",
        date: project.submittedOn || '',
        time: project.time || '', // If you have time, extract it from created_at or another field
    }));

    // const handleFilterApply = (filters: { fields: string; value: string }[]) => {
    //     setAppliedFilters(filters);
    //     setQueryString(prev => ({ ...prev, page: 1 }));
    // };

    return (
        <>
            {isLoading && <Loader />}
            <div className="min-h-screen bg-white p-6">
                {/* Header */}
                <div className="pb-6 flex justify-between items-center">
                    <div>
                    <h1 className="text-2xl font-bold" style={{ color: '#000000' }}>Overview</h1>
                    <p className="text-gray-500 text-[16px] font-[Open_Sans] mt-2 font-semibold">
                        Welcome {userDetail?.full_name ? userDetail.full_name : ""}!
                    </p>
                    </div>

                    <div className="flex justify-end">
                        <CustomButton
                            text="View workflows"
                            onClick={handleCreateProject}
                            isLoading={false}
                            className="text-xs md:text-base"
                        />
                    </div>

                
                </div>


                {/* Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 w-full  gap-4 mb-6">
                    <DashboardCard
                        title="Total Organizations"
                        count={dashboardData?.organization?.total_count || "0"}
                        lastMonth={dashboardData?.organization?.last_month || "0"}
                        thisYear={dashboardData?.organization?.this_year || "0"}
                        icon={<Building2 className="w-6 h-6 text-orange-500" />}
                    />
                    <DashboardCard
                        title="Total Models"
                        count={dashboardData?.project?.total_count || "0"}
                        lastMonth={dashboardData?.project?.last_month || "0"}
                        thisYear={dashboardData?.project?.this_year || "0"}
                        icon={<Sparkles className="w-6 h-6 text-orange-500" />}
                    />
                    <DashboardCard
                        title="Active Users"
                        count={dashboardData?.users?.total_count || "0"}
                        lastMonth={dashboardData?.users?.last_month || "0"}
                        thisYear={dashboardData?.users?.this_year || "0"}
                        icon={<Users2 className="w-6 h-6 text-orange-500" />}
                    />
                </div>

                {/* Bottom Sections */}
                <div className="grid grid-cols-1 xl:grid-cols-1 gap-4">
                    {/* <OrganizationSection organization={organizationListData} /> */}
                    <RecentProjectsSection projects={formattedRecentProjects} />
                </div>

                {/* Filter Panel and Table */}
                {/* <div className="w-full mt-6">
                    <ProjectTable
                        projects={projectListData}
                        type={3}
                        open={true} // or controlled by some dialog or condition
                        initialFilters={appliedFilters}
                        onFilterApply={handleFilterApply}
                        onClose={() => {
                            console.log("ProjectTable closed");
                        }}
                    />
                </div> */}

            </div>
        </>
    );
};

export default OverviewPage;
