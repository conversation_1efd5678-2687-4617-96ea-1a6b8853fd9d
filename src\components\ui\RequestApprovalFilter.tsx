"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Typography, Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CircularProgress from "@mui/material/CircularProgress";
import type { SelectChangeEvent } from "@mui/material";
import type { FormFieldWithValue } from "../formBuilder/types";
import FilterFieldRender from "../forms/searchFields";
import "./style.css";

interface FilterItem {
  [key: string]: string | number | boolean;
}

interface UserManagementFilterProps {
  open: boolean;
  onClose: () => void;
  onFilterApply?: (filters: FilterItem) => void;
  initialFilters?: FilterItem;
  requestTypeOptions?: { id: number; name: string }[];
}

const RequestManagementFilter: React.FC<UserManagementFilterProps> = ({
  open,
  onClose,
  onFilterApply,
  initialFilters = {},
  requestTypeOptions = [],
}) => {
  const [loading] = useState<boolean>(false);
  const [formConfig, setFormConfig] = useState<{ fields: FormFieldWithValue[] }>({
    fields: [],
  });

  useEffect(() => {
    if (!open) return;

    const staticFields: FormFieldWithValue[] = [
      {
        id: 1,
        label: "Request Type",
        type: 3, // select field
        value: "",
        filter: true,
        multiple: false,
        required: false,
        options:
          requestTypeOptions?.map((opt) => ({
            id: opt.id,
            value: String(opt.id),
            label: opt.name,
          })) || [],
        placeholder: "Select request type",
      },
      {
        id: 3,
        label: "Status",
        type: 3,
        value: "",
        filter: true,
        multiple: false,
        required: false,
        options: [
          { id: 2, value: "2", label: "Approved" },
          { id: 3, value: "3", label: "Rejected" },
          { id: 1, value: "1", label: "Pending" },
        ],
        placeholder: "Select status",
      },
    ];

    const fieldsWithValues = staticFields.map((field) => {
      const initialValue = initialFilters?.[field.label];
      return {
        ...field,
        value: initialValue ?? (field.multiple ? [] : ""),
      };
    });

    setFormConfig({ fields: fieldsWithValues });
  }, [open, requestTypeOptions]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) =>
        field.id.toString() === name
          ? { ...field, value: type === "checkbox" ? checked : value }
          : field
      ),
    }));
  };

  const handleSelectChange = (
    e: SelectChangeEvent<string | string[]>,
    field: FormFieldWithValue
  ) => {
    const { value } = e.target;
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((f) =>
        f.id === field.id ? { ...f, value } : f
      ),
    }));
  };

  const handleDateChange = (fieldId: number, dateValue: string | null) => {
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) =>
        field.id === fieldId ? { ...field, value: dateValue || "" } : field
      ),
    }));
  };

  const handleClear = () => {
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) => ({
        ...field,
        value: field.multiple ? [] : "",
      })),
    }));
    onClose();
    onFilterApply?.({});
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const filteredFields = formConfig.fields
      .filter((field) => {
        if (!field.filter) return false;
        if (Array.isArray(field.value)) return field.value.length > 0;
        return field.value !== "" && field.value !== undefined && field.value !== null;
      })
      .reduce((acc, field) => {
        if (field.value instanceof File) return acc;

        let finalValue = field.value;

        // Map string value back to the original ID (number) using options
        if (field.type === 3 && field.options?.length) {
          const selectedOption = field.options.find((opt) => opt.value === field.value);
          finalValue = selectedOption?.id ?? field.value;
        }

        acc[field.label] = Array.isArray(finalValue)
          ? finalValue.join(",")
          : finalValue;

        return acc;
      }, {} as FilterItem);

    onClose();
    onFilterApply?.(filteredFields);
  };

  if (!open) return null;

  return (
    <div className="fixed top-0 right-0 h-full w-[350px] bg-white shadow-lg z-[1000]">
      <Box className="flex justify-between items-center p-2 pl-4 pr-4 border-b border-gray-200">
        <Typography variant="h6" className="font-bold text-[#000000]">
          Filter
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <CircularProgress size={80} style={{ color: "#F45C24" }} />
        </div>
      ) : (
        <Card
          sx={{ overflow: "auto", height: "calc(100% - 120px)", padding: 2 }}
        >
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4">
              {formConfig.fields.map((field) => (
                <div key={field.id}>
                  <FilterFieldRender
                    field={field}
                    onChange={handleChange}
                    onSelectChange={(e) => handleSelectChange(e, field)}
                    onDateChange={(dateValue) =>
                      handleDateChange(field.id, dateValue)
                    }
                  />
                </div>
              ))}
            </div>
            <Box className="flex justify-between p-4 border-t absolute bottom-0 left-0 right-0 bg-white border-t-gray-200">
              <Button
                className="text-transform-none"
                variant="outlined"
                onClick={handleClear}
                style={{
                  width: "48%",
                  fontSize: "15px",
                  color: "#F45C24",
                  borderColor: "#F45C24",
                }}
              >
                Clear
              </Button>
              <Button
                className="text-transform-none"
                type="submit"
                variant="contained"
                style={{
                  width: "48%",
                  fontSize: "15px",
                  background: "linear-gradient(to right, #F45C24, #FFCB80)",
                }}
              >
                Apply
              </Button>
            </Box>
          </form>
        </Card>
      )}
    </div>
  );
};

export default RequestManagementFilter;
