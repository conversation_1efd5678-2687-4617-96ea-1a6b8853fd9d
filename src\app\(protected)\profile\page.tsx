"use client";

import { useRef, useState, useEffect } from 'react'
import { <PERSON>, TextField, Typography } from '@mui/material'
import CustomButton from '../../../components/ui/Button';
import AvatarFallback from '../../../components/ui/AvatarFallback';
import type { RootState } from '../../../store/store';
import { useAppSelector, useAppDispatch } from '../../../store/store';
import { updateProfile, profileImageUpload } from '../../../services/profileApiService';
import { fetchUserById } from '../../../store/userthunk';
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import { useRouter } from 'next/navigation';
// import { postMessage } from '../../../services/pushNotifications';

const PersonalCard = () => {
  const userDetail = useAppSelector((state: RootState) => state.user?.userDetail ?? null);
  
  const dispatch = useAppDispatch();
  const userId = Cookies.get('user_id');
  const router = useRouter();

  const [formData, setFormData] = useState({
    fullName: "",
    userName: "",
    email: "",
    role: "",
    organization: "",
    country: "",
    user_profile_img_path:""
  });

  // NEW: hold the uploaded image-path returned by your API
  const [uploadedImagePath, setUploadedImagePath] = useState<string>("");

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!userDetail && userId) {
      dispatch(fetchUserById(userId));
    }
  }, [dispatch, userId, userDetail]);

  useEffect(() => {
    if (userDetail) {
      setFormData({
        fullName: userDetail.full_name || "",
        userName: userDetail.user_name || "",
        email: userDetail.email || "",
        role: userDetail.role?.name || "",
        organization: userDetail.organization?.name || "",
        country: userDetail.country || "",
        user_profile_img_path: userDetail.user_profile_img_path || "",
      });
  
      // Show base64 image if available
      if (userDetail.user_profile_img_path && !selectedImage) {
        const raw = userDetail.user_profile_img_path.trim();
        const isDataURL = raw?.startsWith("data:image");
      
        const imageUrl = isDataURL ? raw : `data:image/jpeg;base64,${raw}`; // or 'image/png' depending on backend
        setSelectedImage(imageUrl);
      }
    }
  }, [userDetail]);
  
  

  const handleCancel = () => {
    router.push("/dashboard");
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !file.type?.startsWith('image/')) {
      toast.error("Please select an image file (jpg/png).");
      return;
    }

    // preview immediately
    const reader = new FileReader();
    reader.onloadend = () => setSelectedImage(reader.result as string);
    reader.readAsDataURL(file);

    // upload
    const formData = new FormData();
    formData.append("file", file);  // or "image", depending on your backend

    try {
      const response = await profileImageUpload(formData);
      // assume your API returns { image_path: "...", message: "..." }
      const path = response.data.image_path;
      setUploadedImagePath(path);
      toast.success("Image uploaded successfully!");
      // optionally refresh userDetail
      if (userId) await dispatch(fetchUserById(userId));
    } catch (err) {
      console.error("Image upload failed:", err);
      toast.error("Failed to upload image.");
    }
  };

  const handleUploadClick = () => fileInputRef.current?.click();

  const handleSave = async () => {
    const payload = {
      id: userId,
      full_name: formData.fullName,
      user_name: formData.userName,
      country: formData.country,
      user_profile_img_path: uploadedImagePath  // ← include your uploaded-image-path here
    };

    try {
      await updateProfile(payload);
      if (userId) await dispatch(fetchUserById(userId));
      toast.success("Profile updated successfully!");
      // postMessage()
      router.push("/dashboard");
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error("Failed to update profile. Please try again.");
    }
  };

  if (!userDetail) return <div className="text-center text-gray-500 mt-10">Loading profile...</div>;

  return (
    <Box className="w-full max-w-5xl mx-auto bg-white rounded-lg border border-[#E2E8F0] ">
      <div className='text-lg text-gray-700 font-semibold border-b border-[#E2E8F0] px-6 py-4'>Profile</div>

      <div className="flex items-center gap-6 border-b border-[#E2E8F0] px-6 py-6 mb-6">
        {selectedImage ? (
          <img src={selectedImage} alt="Preview" className="w-20 h-20 rounded-full border border-gray-300 shadow-md object-contain" />
        ) : (
          <AvatarFallback
            name={formData.fullName}
            className="w-20 h-20 rounded-full border border-gray-300 shadow-md text-xl"
          />
        )}

        <div>
          <h3 className="text-xl font-bold text-gray-900 pb-2">{formData.fullName || "Full Name"}</h3>
          <button
            type="button"
            onClick={handleUploadClick}
            className="px-4 py-2 bg-[#F06D1A] text-white text-sm font-bold rounded-md shadow hover:bg-[#d95f14] transition whitespace-nowrap"
          >
            Upload New Picture
          </button>
          <input
            type="file"
            accept="image/jpeg,image/jpg,image/png"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
        </div>
      </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 px-6 py-2 text-[#000000]">
          {[
            { label: "Full Name", name: "fullName", disabled: false },
            { label: "User Name", name: "userName", disabled: false },
            { label: "Email", name: "email", disabled: true },
            { label: "Role", name: "role", disabled: true },
            { label: "Organization Name", name: "organization", disabled: true },
            { label: "Country", name: "country", disabled: false },
          ].map(({ label, name, disabled }) => (
            <div key={name}>
              <Typography className="text-[15px] font-semibold font-['Open_Sans'] mb-3 text-[#000000]">{label}</Typography>
              <TextField
                fullWidth
                name={name}
                value={formData[name as keyof typeof formData]}
                onChange={handleChange}
                variant="outlined"
                size="small"
                className="bg-[#FEF4ED] rounded-md "
                disabled={disabled}
              />
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-8 mb-4 gap-4">
          <button type="button" onClick={() => { handleCancel() }} className="border border-[#F06D1A] text-[#F06D1A] px-4 py-2 rounded-md font-semibold cursor-pointer hover:bg-[#F06D1A] hover:text-white transition">
            Cancel
          </button>
          <CustomButton text="Save" isLoading={false} onClick={handleSave} />
        </div>
      </Box>
    
  );
};

export default PersonalCard;
