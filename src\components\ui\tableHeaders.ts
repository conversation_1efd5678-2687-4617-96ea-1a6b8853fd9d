// components/common/tableHeaders.ts
export const userTableHeaders = [
  { id: "1", name: "Username" },
  { id: "2", name: "User ID" },
  { id: "3", name: "Email" },
  { id: "4", name: "Last Active" },
  { id: "5", name: "Status" },
  { id: "6", name: "Role" },
  { id: "7", name: "Action" },
  { id: "8", name: "Created At" },
  { id: "9", name: "Organization" },
];
// export default userTableHeaders;


export const activityTableHeaders = [
    "Timestamp",
    "User",
    "Activity",
    "Organization",
    "Project ID",
    "DataSet",
  ];

  export const requestTableHeaders = [
    // { id: "1", name: "Request ID" },
    { id: "1", name: "Organization" },
    { id: "2", name: "Request Type" },
       { id: "3", name: "Requested Value" },
    { id: "4", name: "Submitted On" },
    { id: "5", name: "Status" },
    // { id: "6", name: "Description" },
    // { id: "8", name: "Priority" },
    // { id: "9", name: "Submitted By" },
  ];
  export const projectTableHeaders = [
    { id: "7", name: "Action" },
    { id: "1", name: "Organization ID" },
    { id: "2", name: "Organization Name" },
    { id: "3", name: "Admin Email" },
    { id: "4", name: "KYC Documents" },
    { id: "5", name: "Status" },
    { id: "6", name: "Last Updated" },
    { id: "8", name: "Created At" },
    { id: "9", name: "Organization Type" },
  ];

  export const userManagementTableHeaders = [
    { id: "1", name: "User Name" },
    { id: "2", name: "Email" },
    { id: "4", name: "Status" },
    { id: "5", name: "country" },
    { id: "5", name: "Role" },
    { id: "6", name: "Action" },
  ];
  export default userManagementTableHeaders;
