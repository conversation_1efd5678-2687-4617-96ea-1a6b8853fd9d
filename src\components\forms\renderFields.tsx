"use client";
import {
	TextField,
	Radio,
	RadioGroup,
	FormControlLabel,
	FormLabel,
	FormControl,
	Button,
	Typography,
	Box,
	Select,
	MenuItem,
	Switch,
	Checkbox,
	Autocomplete,
} from "@mui/material";
import type { SelectChangeEvent } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import type { FormField, FormFieldWithValue } from "../formBuilder/types";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { toast } from "react-toastify";
import { dataSetFileDelete } from "../../services/dataSetapiService";

interface FormFieldRendererProps {
	field: FormFieldWithValue;
	error?: string | null;
	fileformat?: string;
	onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	onSelectChange: (
		e: SelectChangeEvent<string | string[]>,
		field: FormField,
	) => void;
	options?: FormField["options"];
	setModelOpen?: (open: boolean) => void;
	className?: string;
	disabled?: boolean;
	// onFileRemove?: (fieldId: string, fileUrl: string) => Promise<void>;
}

export default function FormFieldRenderer({
	field,
	error,
	// fileformat,
	// onFileRemove,
	onChange,
	onSelectChange,
	options = field.options,
	setModelOpen,
	disabled = false,
}: FormFieldRendererProps) {
	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
	};
	switch (field.type) {
		case 1: // Text/Number
			return (
				<FormControl
					component="fieldset"
					margin="normal"
					error={!!error}
					fullWidth
				>
					<FormLabel component="legend">
						{field.label}
						{field.required && <span className="text-red-500 ml-1">*</span>}
					</FormLabel>
					<TextField
						fullWidth
						name={field.id.toString()}
						required={field.required}
						variant="outlined"
						margin="normal"
						value={field.value || ""}
						onChange={onChange}
						error={!!error}
						helperText={error}
						placeholder={field.placeholder}
						type={
							field?.field_type?.toLowerCase() === "number" ? "number" : "text"
						}
						inputProps={{
							min: field.min,
							max: field.max,
						}}
						disabled={disabled}
						className="inputfield inputBackground"
					/>
				</FormControl>
			);
		case 6: // Radio
			return (
				<FormControl
					component="fieldset"
					margin="normal"
					error={!!error}
					fullWidth
				>
					<FormLabel component="legend">
						{field.label}
						{field.required && <span className="text-red-500 ml-1">*</span>}
					</FormLabel>
					<RadioGroup
						name={field.id.toString()}
						value={field.value || ""}
						onChange={onChange}
						row
					>
						{field.options?.map((option) => (
							<FormControlLabel
								key={option.id}
								value={option.value}
								control={<Radio />}
								label={option.value}
							/>
						))}
					</RadioGroup>
					{error && (
						<Typography variant="caption" color="error">
							{error}
						</Typography>
					)}
				</FormControl>
			);
		case 3: // Select/Multi-select
			return (
				<FormControl fullWidth margin="normal" error={!!error}>
					<FormLabel
						className="select-lable"
						style={{ paddingBottom: "10px" }}
						component="legend"
					>
						{field.label}
						{field.required && <span className="text-red-500 ml-1">*</span>}
					</FormLabel>
					<Select
						name={field.id.toString()}
						className="inputBackground"
						sx={{
							".MuiSvgIcon-root": {
								color: "#1f2021", // Optional: changes dropdown arrow color
								
							},
						}}
					value={
						typeof field.value === "string" || Array.isArray(field.value)
							? field.value
							: field.multiple
								? []
								: ""
					}
					onChange={(e) =>
						onSelectChange(e as SelectChangeEvent<string | string[]>, field)
					}
					multiple={field.multiple}
					displayEmpty
					renderValue={(selected) => {
						const textColor = "#1f2021";

						if (field.multiple) {
							return (selected as string[]).length ? (
								<span style={{ color: textColor }}>
									{(selected as string[]).join(", ")}
								</span>
							) : (
								<span style={{ color: "#aaa" }}>
									{field.placeholder || "Select options"}
								</span>
							);
						}

						return selected ? (
							<span style={{ color: textColor }}>{selected as string}</span>
						) : (
							<span style={{ color: "#aaa" }}>
								{field.placeholder || "Select an option"}
							</span>
						);
					}}
					inputProps={{
						"aria-label": field.label,
					}}
					MenuProps={{
						PaperProps: {
							style: {
								maxHeight: 300,
								
							},
						},
					}}
					// className="inputfield inputBackground"
					>
					{field.placeholder && !field.multiple && (
						<MenuItem disabled value="">
							<em>{field.placeholder}</em>
						</MenuItem>
					)}
					{field.options?.map((option) => (
						<MenuItem key={option.id} value={option.value}>
							{option.value}
						</MenuItem>
					))}
				</Select>

					{
				error && (
					<Typography variant="caption" color="error" className="mt-1 block">
						{error}
					</Typography>
				)
			}
				</FormControl >
			);
		case 5: // Date picker
			return (
				<LocalizationProvider dateAdapter={AdapterDayjs}>
					<FormControl fullWidth margin="normal" error={!!error}>
						<FormLabel style={{ paddingBottom: "14px" }}>
							{field.label}
							{field.required && <span className="text-red-500 ml-1">*</span>}
						</FormLabel>
						<DatePicker
							value={
								typeof field.value === "string" ||
									typeof field.value === "number"
									? dayjs(field.value, field.dateFormat)
									: null
							}
							onChange={(date: Dayjs | null) => {
								const dateValue = date ? date.format(field.dateFormat) : null;
								onChange({
									target: {
										name: field.id.toString(),
										value: dateValue,
									},
								} as React.ChangeEvent<HTMLInputElement>);
							}}
							format={field.dateFormat || "YYYY-MM-DD"}
							// mask={field.mask || "__-__-____"}
							slots={{ textField: TextField }}
							slotProps={{
								textField: {
									fullWidth: true,
									error: !!error,
									helperText: error,
								},
							}}
							className="inputfield inputBackground"
						/>
					</FormControl>
				</LocalizationProvider>
			);
		case 8: // Switch (boolean toggle)
			return (
				<FormControl fullWidth margin="normal" error={!!error}>
					<FormControlLabel
						control={
							<Switch
								checked={Boolean(field.value)}
								onChange={(e) => {
									onChange({
										target: {
											name: field.id.toString(),
											checked: e.target.checked,
											type: "checkbox",
										},
									} as React.ChangeEvent<HTMLInputElement>);
								}}
								color="primary"
								name={field.id.toString()}
							/>
						}
						label={
							<div className="flex items-center">
								{field.label}
								{field.required && <span className="text-red-500 ml-1">*</span>}
							</div>
						}
					/>
					{error && (
						<Typography variant="caption" color="error" className="block mt-1">
							{error}
						</Typography>
					)}
				</FormControl>
			);
		case 2: // Checkbox
			return (
				<FormControlLabel
					control={
						<Checkbox
							name={field.id.toString()}
							checked={!!field.value}
							onChange={onChange}
							color="primary"
						/>
					}
					label={
						<span>
							{field.label}
							{field.required && <span className="text-red-500 ml-1">*</span>}
							{error && (
								<Typography variant="caption" color="error" display="block">
									{error}
								</Typography>
							)}
						</span>
					}
				/>
			);
		// case 9: // Email
		// 	return (
		// 		<FormControl
		// 			component="fieldset"
		// 			margin="normal"
		// 			error={!!error}
		// 			fullWidth
		// 		>
		// 			<FormLabel component="legend">
		// 				{field.label}
		// 				{field.required && "*"}
		// 			</FormLabel>
		// 			<TextField
		// 				fullWidth
		// 				name={field.id.toString()}
		// 				required={field.required}
		// 				variant="outlined"
		// 				margin="normal"
		// 				value={field.value || ""}
		// 				onChange={onChange}
		// 				error={!!error}
		// 				helperText={error}
		// 				placeholder={field.placeholder}
		// 				type="email"
		// 			/>
		// 		</FormControl>
		// 	);
		case 9: // Autocomplete
			return (
				<FormControl fullWidth margin="normal" error={!!error}>
					<FormLabel component="legend" className="mb-2">
						{field.label}
						{field.required && <span className="text-red-500 ml-1">*</span>}
					</FormLabel>
					<Autocomplete
						multiple={field.multiple}
						className="inputBackground"
						options={options?.map((opt) => opt.value) || []}
						value={
							field.multiple
								? Array.isArray(field.value)
									? field.value
									: []
								: field.value || null
						}
						onChange={(_, newValue) => {
							onChange({
								target: {
									name: field.id.toString(),
									value: newValue,
								},
							} as React.ChangeEvent<HTMLInputElement>);
						}}
						renderInput={(params) => (
							<TextField
								{...params}
								placeholder={field.placeholder || "Start typing..."}
								error={!!error}
								helperText={error}
							/>
						)}
					/>
				</FormControl>
			);

		case 7: {
			const isMultiple = field.multiple ?? false;
			const value = field.value;
			const files = isMultiple
				? Array.isArray(value)
					? value
					: []
				: value
					? [value]
					: [];

			const handleRemoveFile = async (index: number) => {
				console.log("Removing file at index:", files, index);

				const fileToRemove = files[index];
				const fileUrl =
					typeof fileToRemove === "string"
						? fileToRemove
						: fileToRemove instanceof File
							? fileToRemove.name
							: "";

				try {
					if (fileUrl) {
						await dataSetFileDelete({ filepath: fileUrl });
					}

					const updated = isMultiple ? files.filter((_, i) => i !== index) : "";
					onChange({
						target: {
							name: field.id.toString(),
							value: updated,
							files: null,
						},
					} as React.ChangeEvent<HTMLInputElement>);
				} catch (error) {
					console.error("Failed to delete file", error);
					toast.error("Failed to delete file. Please try again.");
				}
			};

			return (
				<FormControl fullWidth margin="normal" error={!!error}>
					<FormLabel component="legend" className="mb-2">
						{field.label}
						{field.required && <span className="text-red-500 ml-1">*</span>}
					</FormLabel>

					{/* Show file input only if no file is selected */}
					{files.length === 0 && (
						<Box
							sx={{
								border: `2px dashed ${error ? "#f44336" : "#1976d2"}`,
								padding: 3,
								textAlign: "center",
								borderRadius: 2,
								cursor: "pointer",
								bgcolor: "#fff3eb",
								"&:hover": {
									bgcolor: "#f7dece",
								},
							}}
							className="inputBackground"
							onClick={() =>
								document.getElementById(`file-upload-${field.id}`)?.click()
							}
						>
							<CloudUploadIcon
								color={error ? "error" : "primary"}
								sx={{ fontSize: 40, mb: 1 }}
							/>
							<Typography
								variant="body1"
								color={error ? "error" : "textSecondary"}
							>
								{field.placeholder ||
									`Click to upload ${isMultiple ? "files" : "a file"}`}
							</Typography>
							<Typography
								variant="caption"
								display="block"
								color="textSecondary"

							>
								Accepted formats: {field.accept || "jpeg, png, docx, pdf"}
							</Typography>
							<input
								id={`file-upload-${field.id}`}
								className="inputBackground"
								type="file"
								name={field.id.toString()}
								hidden
								onChange={onChange}
								accept={field.accept || "jpeg, png, docx, pdf"}
								multiple={isMultiple}
							/>
						</Box>
					)}

					{/* Show selected files if any */}
					{files.length > 0 && (
						<Box mt={2}>
							<Box component="ul" sx={{ listStyle: "none", p: 0, m: 0 }}>
								{files.map((file, index) => {
									const fileObj = file instanceof File ? file : null;
									const fileName = fileObj ? fileObj.name : String(file);
									const fileSize = fileObj
										? formatFileSize(fileObj.size)
										: null;

									return (
										<Box
											key={fileName}
											component="li"
											sx={{
												display: "flex",
												alignItems: "center",
												justifyContent: "space-between",
												border: "1px solid #ddd",
												borderRadius: 1,
												backgroundColor: "#f5f5f5",
												padding: 1,
												mb: 1,
											}}
										>
											<Box
												sx={{ display: "flex", alignItems: "center", gap: 1 }}
											>
												<InsertDriveFileIcon color="action" />
												<Box>
													<Typography variant="body2" title={fileName}>
														{fileName.length > 30
															? `${fileName.slice(0, 30)}...`
															: fileName}
													</Typography>
													{fileSize && (
														<Typography
															variant="caption"
															color="text.secondary"
															sx={{ fontSize: "0.75rem" }}
														>
															{fileSize}
														</Typography>
													)}
												</Box>
											</Box>
											<Button
												color="error"
												size="small"
												// variant="outlined"
												onClick={() => handleRemoveFile(index)}
												sx={{ minWidth: "18px", padding: "4px 8px" }}
											>
												✕
											</Button>
										</Box>
									);
								})}
							</Box>
						</Box>
					)}

					{error && (
						<Typography variant="caption" color="error" className="mt-1 block">
							{error}
						</Typography>
					)}
				</FormControl>
			);
		}

		case 4: // Button
			return (
				<>
					<FormLabel
						component="legend"
						style={{ visibility: "hidden", marginBottom: "15px" }}
					>
						{field.label}
					</FormLabel>

					<Button
						variant="contained"
						fullWidth={field.fullWidth !== false}
						className="prebutton sm:w-auto p-8 px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white"
						startIcon={field.startIcon && <field.startIcon />}
						endIcon={field.endIcon && <field.endIcon />}
						onClick={() => {
							setModelOpen?.(true);
						}}
					>
						{field.label}
					</Button>
				</>
			);
		default:
			return (
				<Typography color="error">Unknown field type: {field.type}</Typography>
			);
	}
}
