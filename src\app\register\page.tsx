"use client";
import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON><PERSON>, Box } from "@mui/material";
import RegisterBackground from "../../assests/Background images/Register-form-bg.png";
import Image from "next/image";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import "./style.css";
import { desFormGet, registerCreate } from "../../services/desApiService";
import FormFieldRenderer from "../../components/forms/renderFields";
import type {
	FormConfig,
	FormField,
	FormSection,
} from "../../components/formBuilder/types";
import type { SelectChangeEvent } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import * as Yup from "yup";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import axios from "axios";
import { dataSetFileUpload } from "../../services/dataSetapiService";
export default function DynamicForm() {
	const [loading, setLoading] = useState<boolean>(true);
	const [, setUploadedFiles] = useState<
		Record<string, { file_path: string; id: string }>
	>({});
	const [formConfig, setFormConfig] = useState<FormConfig>({
		sections: [],
		fields: [],
	});
	const router = useRouter();
	const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);
	useEffect(() => {
		const fetchFormData = async () => {
			setLoading(true);
			try {
				setLoading(true);
				const desformvalue = await desFormGet({ type: 1 });
				setFormConfig(desformvalue.data);
			} catch (err) {
				console.error("Failed to fetch form data", err);
			} finally {
				setLoading(false);
			}
		};

		fetchFormData();
	}, []);
	const { initialValues, validationSchema } = useMemo(() => {
		const initialVals: Record<
			string,
			string | number | boolean | File | File[] | null
		> = {};
		const validationShape: Record<string, Yup.AnySchema> = {};

		for (const field of formConfig.fields) {
			initialVals[field.id] = "";

			if (field.required) {
				switch (field.type) {
					case 1:
						if (field.textfieldtype === "number") {
							let schema = Yup.number()
								.typeError("Must be a valid number")
								.required(`${field.label} is required`);

							if (field.min !== undefined) {
								schema = schema.min(field.min, `Minimum value is ${field.min}`);
							}
							if (field.max !== undefined) {
								schema = schema.max(field.max, `Maximum value is ${field.max}`);
							}
							validationShape[field.id] = schema;
						} else if (
							field.label.includes("email") ||
							field.label.includes("Email")
						) {
							validationShape[field.id] = Yup.string()
								.matches(
									/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
									"Must be a valid email address",
								)
								.required(`${field.label} is required`);
						} else if (
							field.label.includes("Phone Number") ||
							field.label.includes("phone number")||field.label.includes("Contact Number")||field.label.includes("contact number")
						) {
							validationShape[field.id] = Yup.string()
								.matches(
									/^[+]?[0-9]{1,4}?[-.\s\(\)]?(\(?[0-9]{1,3}?\)?[-.\s]?)?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,9}$/,
									"Must be a valid phone number",
								)
								.required(`${field.label} is required`);
						} else {
							validationShape[field.id] = Yup.string().required(
								`${field.label} is required`,
							);
						}
						break;

					case 3:
						validationShape[field.id] = Yup.mixed().required(
							`${field.label} is required`,
						);
						break;

					case 7:
						validationShape[field.id] = Yup.mixed()
							.required("File upload is required")
							.test(
								"fileType",
								"Only jpeg, png, docx, or pdf allowed",
								(value) => {
									if (!value) return false;
									const getExtension = (val: File | string | null) => {
										if (typeof val === "string")
											return val.split(".").pop()?.toLowerCase();
										if (val?.name)
											return val.name.split(".").pop()?.toLowerCase();
										return "";
									};
									if (Array.isArray(value)) {
										return value.every((v) =>
											["jpeg", "jpg", "png", "docx", "pdf"].includes(
												getExtension(v) || "",
											),
										);
									}
									return ["jpeg", "jpg", "png", "docx", "pdf"].includes(
										getExtension(value as string | File) || "",
									);
								},
							)
							.test("fileSize", "File size must be ≤ 5MB", (value) => {
								if (!value) return false;
								if (typeof value === "string") return true;
								const getSize = (val: File | string | null) =>
									val instanceof File ? val.size : 0;
								if (Array.isArray(value)) {
									return value.every(
										(v) =>
											typeof v === "string" || getSize(v) <= 5 * 1024 * 1024,
									);
								}

								return (
									value instanceof File && getSize(value) <= 5 * 1024 * 1024
								);
							});
						break;

					default:
						validationShape[field.id] = Yup.string().required(
							`${field.label} is required`,
						);
				}
			}
		}

		return {
			initialValues: initialVals,
			validationSchema: Yup.object(validationShape),
		};
	}, [formConfig.fields]);

	const formik = useFormik({
		enableReinitialize: true,
		initialValues,
		validationSchema,
		onSubmit: async (values) => {
			try {
				// setLoading(true);
				const payload = {
					sections: formConfig.sections,
					fields: formConfig.fields.map((field) => ({
						...field,
						value: values[field.id],
					})),
					type: 1,
				};

				const response = await registerCreate(payload);
				toast.success(response.data.message || "Registered successfully");
				router.push("/marketingPage");
				setSubmitSuccess(true);
			} catch (error) {
				if (axios.isAxiosError(error)) {
					const responseData = error.response?.data;
					const message =
						responseData?.message ||
						responseData?.error ||
						error.message ||
						"Something went wrong.";

					toast.error(message);
				} else if (error instanceof Error) {
					toast.error(error.message);
				} else {
					toast.error("Registration failed. Please try again.");
				}
			} finally {
				setLoading(false);
			}
		},
	});

	const handleSelectChange = (
		e: SelectChangeEvent<string | string[]>,
		field: FormField,
	) => {
		formik.setFieldValue(field.id.toString(), e.target.value);
	};

	const handleChange = async (
		e:
			| React.ChangeEvent<HTMLInputElement>
			| {
					target: {
						name: string;
						value: string | number | boolean | File | File[];
						type?: string;
						checked?: boolean;
						files?: FileList;
					};
			  },
	) => {
		const { name, value, type, checked, files } = e.target as HTMLInputElement;

		if (files && files.length > 0) {
			const file = files[0];
			await handleFileUpload(name, file); 
			
		} else if (type === "checkbox" || type === "switch") {
			formik.setFieldValue(name, checked);
		} else {
			formik.setFieldValue(name, value);
		}
	};

	const handleFileUpload = async (fieldName: string, file: File): Promise<void> => {
		const allowedExtensions = ["jpeg", "jpg", "png", "docx", "pdf"];
		const ext = file.name.split(".").pop()?.toLowerCase();
	  
		if (!ext || !allowedExtensions.includes(ext)) {
		  toast.error("Invalid file type. Allowed: jpeg, jpg, png, docx, pdf");
		  return;
		}
		if (file.size > 5 * 1024 * 1024) {
		  toast.error("File size must be ≤ 5MB");
		  return;
		}
		try {
		  setLoading(true);
		  const formData = new FormData();
		  formData.append("file", file);
		  formData.append("FormType", "IMAGE");
	  
		  const response = await dataSetFileUpload(formData);
		  const fileUrl = response.data.file_path;
		  
		  formik.setFieldValue(fieldName, fileUrl);
		  setUploadedFiles((prev) => ({
			...prev,
			[fieldName]: { file_path: fileUrl, id: Date.now().toString() },
		  }));
	  
		  toast.success("File uploaded successfully");
		} catch (err) {
		  console.error("File upload failed", err);
		  toast.error("File upload failed. Please try again.");
		  throw err;
		} finally {
		  setLoading(false);
		}
	  };

	return (
		<div className="min-h-screen flex items-center justify-center p-4 relative d-flex ">
			<Image
				src={RegisterBackground}
				alt="Background"
				fill
				className="object-cover -z-10"
				priority
			/>
			{loading && (
				<div className="fixed inset-0 flex items-center justify-center z-50 backdrop-blur-md bg-opacity-50">
					<CircularProgress size={80} style={{ color: "#F45C24" }} />
				</div>
			)}
			<div className="w-full max-w-5xl">
				<Box className="flex justify-between items-center mb-6 p-4  rounded-t-lg backdrop-blur-sm">
					<Box>
						<Typography variant="h5" className="font-bold">
							Register your Organization
						</Typography>
						<Typography variant="subtitle1" className="text-gray-600">
							Complete your organization profile
						</Typography>
					</Box>
					<Button
						style={{ background: "white", color: "black" }}
						variant="contained"
						startIcon={<ArrowBackIcon />}
						onClick={() => router.push("/marketingPage")}
					>
						Back
					</Button>
				</Box>
				<Card className="w-full max-w-5xl backdrop-blur-sm bg-white/90 overflow-auto shadow-xl hover:shadow-4xl transition-shadow duration-300">
					{submitSuccess && (
						<Alert
							severity="success"
							className="mb-4"
							onClose={() => setSubmitSuccess(false)}
						>
							Form submitted successfully!
						</Alert>
					)}

					<form onSubmit={formik.handleSubmit}>
						{formConfig.sections
							.sort((a, b) => (a.position || 0) - (b.position || 0))
							.map((section: FormSection) => (
								<div key={section.id} className="mb-4">
									<Typography variant="h6" className="mb-4 p-4 ">
										{section.label}
									</Typography>
									<Divider className="mb-4" />

									<div className="grid pt-4 pl-8 pr-8 grid-cols-1 md:grid-cols-2 gap-4">
										{formConfig.fields
											.filter(
												(field: FormField) => field.section_id === section.id,
											)
											.sort((a, b) => (a.position || 0) - (b.position || 0))
											.map((field: FormField) => (
												<div key={field.id}>
													<FormFieldRenderer
														field={{ ...field, value: formik.values[field.id] }}
														error={
															formik.touched[field.id] &&
															formik.errors[field.id]
																? formik.errors[field.id]
																: ""
														}
														fileformat={"jpeg, jpg, png, docx, pdf"}
														onChange={handleChange}
														// onFileRemove={handleFileDelete}
														onSelectChange={(e) =>
															handleSelectChange(
																e as SelectChangeEvent<string | string[]>,
																field,
															)
														}
													/>
												</div>
											))}
									</div>
								</div>
							))}
						<Box className="flex justify-center p-5 ">
							<Button
								onClick={() => formik.handleSubmit()}
								// type="submit"
								variant="contained"
								style={{ padding: "10px", width: "200px", fontSize: "15px" }}
								className="sm:w-auto px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white"
							>
								Submit
							</Button>
						</Box>
					</form>
				</Card>
			</div>
		</div>
	);
}
