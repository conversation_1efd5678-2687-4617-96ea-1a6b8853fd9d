'use client';

import React, { useEffect, useState } from 'react';
import { statusOptions } from '../../../constants/fieldtype';

import { Box, Button, Typography, CircularProgress } from '@mui/material';
import { desFormGet } from '../../../services/desApiService';
import type { SelectChangeEvent } from "@mui/material";
import { toast } from 'react-toastify';
import { FormField, FormFieldWithValue } from '../../../components/formBuilder/types';
import FilterFieldRender from '../../../components/forms/searchFields';


const headers = ['Project', 'Organization', 'Status', 'Speciality', 'Sub-Speciality', 'Model type', 'Users'];

interface ProjectTableProps {
  projects: {
    modelName: string;
    organization?: string;
    status: number | string;
    specialty?: string;
    subSpecialty?: string;
    type?: string;
    timeCount?: number;
  }[];
  type?: number;
  open: boolean;
  onClose: () => void;
  onFilterApply?: (filteredFields: { fields: string; value: string }[]) => void;
  initialFilters?: { fields: string; value: string }[];
}

const ProjectTable: React.FC<ProjectTableProps> = ({
  projects,
  type,
  onFilterApply,
  initialFilters = []
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [formConfig, setFormConfig] = useState<{ fields: FormField[] }>({
    fields: [],
  });


  console.log("formConfig", formConfig);

  useEffect(() => {
    
  
    const fetchFormData = async () => {
      setLoading(true);
      try {
        const desformvalue = await desFormGet({ type: type });
        console.log("desformvalue", desformvalue);
        const fieldsWithValues = desformvalue.data.fields.map(field => {
          const matchingFilter = initialFilters.find(f => f.fields === field.label);
          return {
            ...field,
            value: matchingFilter
              ? field.multiple
                ? matchingFilter.value.split(',')
                : matchingFilter.value
              : field.multiple
                ? []
                : ''
          };
        });
  
        setFormConfig({
          ...desformvalue.data,
          fields: fieldsWithValues
        });
      } catch (err) {
        console.error("Failed to fetch form data", err);
      } finally {
        setLoading(false);
      }
    };
  
    fetchFormData();
  }, [type, open, JSON.stringify(initialFilters)]);
  

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormConfig((prevConfig: { fields: FormField[] }) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field: FormField) => {
        if (field.id.toString() === name) {
          return {
            ...field,
            value: type === "checkbox" || type === "switch" ? checked : value
          };
        }
        return field;
      })
    }));
  };

  const handleSelectChange = (
    e: SelectChangeEvent<string | string[]>,
    field: FormField
  ) => {
    const { value } = e.target;
    setFormConfig((prevConfig: { fields: FormField[] }) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((f: FormField) => {
        if (f.id === field.id) {
          return {
            ...f,
            value: value
          };
        }
        return f;
      })
    }));
  };

  // const handleClear = () => {
  //   setFormConfig((prevConfig: { fields: FormField[] }) => ({
  //     ...prevConfig,
  //     fields: prevConfig.fields.map((field: FormField) => ({
  //       ...field,
  //       value: field.multiple ? [] : ''
  //     }))
  //   }));
  //   // onClose();
  //   if (typeof onFilterApply === 'function') {
  //     onFilterApply([]);
  //   }
  // };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const filteredFields = formConfig.fields
      .filter((item) => {
        if (!item.filter) return false;
        const value = (item as FormFieldWithValue).value;
        if (Array.isArray(value)) return value.length > 0;
        return value !== '' && value !== undefined && value !== null;
      })
      .map((item) => {
        const value = (item as FormFieldWithValue).value;

        let formattedValue: string;

        if (Array.isArray(value)) {
          if (value.length > 0 && typeof value[0] === 'string') {
            formattedValue = value.join(',');
          } else if (value[0] instanceof File) {
            formattedValue = (value as File[]).map(file => file.name).join(', ');
          } else {
            formattedValue = '';
          }
        } else {
          formattedValue = String(value);
        }

        return {
          fields: item.label,
          value: formattedValue
        };
      });

    try {

      if (typeof onFilterApply === 'function') {
        onFilterApply(filteredFields);
      }
    } catch (error) {
      toast.error("Filter application failed", error);
    } finally {
      setLoading(false);
    }
  };

  if (!open) return null;

  return (
    <Box className="grid grid-cols-1 lg:grid-cols-12 gap-4 mt-6 w-full">
      {loading && (
        <div className="flex items-center justify-center h-full">
          <CircularProgress size={80} style={{ color: "#F45C24" }} />
        </div>
      )}
      {/* Filter Panel */}
      <Box className="md:col-span-4 w-full">
        <form onSubmit={handleSubmit}>
          <Box className="bg-white rounded-xl border border-gray-300 shadow-lg p-4 space-y-4">
            {formConfig.fields
              .filter((item: FormField) => item.filter === true && (item.label !== "Dataset Name" && item.label !== "Project Name" && item.label !== "Organization Name"))
              .map((field: FormField) => (
                <div key={field.id}>
                  <FilterFieldRender
                    field={field as FormFieldWithValue}
                    onChange={handleChange}
                    onSelectChange={(e) => handleSelectChange(e, field)}
                    fileformat={"jpeg, jpg, png, docx, pdf"}
                  />
                </div>
              ))}

            <Button
              variant="contained"
              className="w-full bg-gradient-to-r from-orange-600 to-orange-300 text-white  font-bold"
              sx={{
                textTransform: 'none',
                height: '40px',
                borderRadius: '8px',
                fontFamily: 'Open Sans',
                fontWeight: 'bold',
              }}
              type="submit"
            >
              Apply Filters
            </Button>

          </Box>
        </form>
      </Box>

      {/* Table Container */}
      <Box className="md:col-span-8 w-full">
        <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto" style={{ height: '34rem' }}>
          <Box className="min-w-[900px] h-full overflow-y-auto">
            {/* Table Headers */}
            <Box className="grid grid-cols-7 px-5 py-3 border-b border-[#d7dade] sticky top-0 bg-white z-10">
              {headers.map((header, idx) => (
                <Typography
                  key={idx}
                  sx={{
                    fontSize: "17px",
                    fontWeight: 600,
                    color: "black",
                  }}
                >
                  {header}
                </Typography>
              ))}
            </Box>

            {/* Table Rows */}
            <Box className="overflow-y-auto">
              {projects.map((row, rowIdx) => (
                <Box
                  key={rowIdx}
                  className={`grid grid-cols-7 items-center px-5 py-4 ${rowIdx !== 0 ? 'border-t border-gray-300' : ''
                    }`}
                >
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.modelName}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.organization || '-'}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {typeof row.status === 'number' || !isNaN(Number(row.status))
                      ? statusOptions.find(option => option.code === Number(row.status))?.label || row.status
                      : row.status}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.specialty || '-'}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.subSpecialty || '-'}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.type || '-'}
                  </Typography>
                  <Typography className="text-sm text-gray-800" sx={{ fontSize: '16px', fontFamily: 'Open Sans' }}>
                    {row.timeCount || 0}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ProjectTable;
