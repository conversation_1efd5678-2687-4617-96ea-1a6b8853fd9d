"use client";
import type React from "react";
import { useRef } from "react";
import { Box, IconButton, Paper, Typography } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useDrag, useDrop, DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import rowicon from "../../assests/Icons/rows-02.png";
import Image from "next/image";

type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface Section {
	id: number;
	label: string;
	position: number;
	is_default: boolean;
}
interface FormValues {
	[key: string]: string | number | boolean | File | File[] | null | undefined;
  }
interface Field {
	id: number;
	label: string;
	type: FieldType;
	required: boolean;
	section_id: number;
	options?: { id: number; value: string }[];
	placeholder?: string;
	textfieldtype?: string;
	min?: number;
	filter?: boolean;
	dropdown_type?: string;
	is_default?: boolean;
	max?: number;
	size?: string|number;
	position?: number;
	dateFormat?: "DD/MM/YYYY" | "MM/DD/YYYY" | "YYYY/MM/DD";
	multiple?: boolean;
		accept?: string;
		fullWidth?: boolean;
		mask?: string;
		onClick?: (values: FormValues) => void;
		startIcon?: React.ElementType;
		endIcon?: React.ElementType;
}

interface FormBuilderProps {
	sections: Section[];
	fields: Field[];
	// formData: Record<string, string | number | boolean | string[] | undefined>;
	// handleChange: (name: string, value: string | number | boolean | string[] | undefined, sectionId?: number) => void;
	handleEditClick?: (field: Field) => void;
	handleDelete: (id: number) => void;
	handleSectionEdit: (section: Section) => void;
	handleSectionDelete: (id: number) => void;
	handleSectionClick: (sectionId: number) => void;
	activeSectionId: number | null;
	moveField: (fromIndex: number, toIndex: number, sectionId: number) => void;
	moveSection: (fromIndex: number, toIndex: number) => void;
}

const ItemTypes = {
	FIELD: "FIELD",
	SECTION: "SECTION",
};

interface DragItem {
	id: number;
	index: number;
	type: string;
	sectionId?: number;
}

const DraggableSection: React.FC<{
	section: Section;
	index: number;
	moveSection: (fromIndex: number, toIndex: number) => void;
	children: React.ReactNode;
}> = ({ section, index, moveSection, children }) => {
	const ref = useRef<HTMLDivElement>(null);

	const [{ isDragging }, drag] = useDrag({
		type: ItemTypes.SECTION,
		item: { id: section.id, index, type: ItemTypes.SECTION },
		collect: (monitor) => ({
			isDragging: monitor.isDragging(),
		}),
	});

	const [, drop] = useDrop({
		accept: ItemTypes.SECTION,
		hover: (item: DragItem, monitor) => {
			if (!ref.current) return;
			if (item.type !== ItemTypes.SECTION) return;

			const dragIndex = item.index;
			const hoverIndex = index;

			if (dragIndex === hoverIndex) return;

			const hoverBoundingRect = ref.current?.getBoundingClientRect();
			const hoverMiddleY =
				(hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
			const clientOffset = monitor.getClientOffset();
			const hoverClientY = clientOffset ? clientOffset.y - hoverBoundingRect.top : 0;

			if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
			if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

			moveSection(dragIndex, hoverIndex);
			item.index = hoverIndex;
		},
	});

	drag(drop(ref));

	return (
		<div
			ref={ref}
			style={{
				opacity: isDragging ? 0.5 : 1,
				cursor: "move",
				transition: "transform 0.2s ease",
				transform: isDragging ? "scale(1.02)" : "scale(1)",
			}}
		>
			{children}
		</div>
	);
};

const DraggableField: React.FC<{
	field: Field;
	index: number;
	moveField: (fromIndex: number, toIndex: number, sectionId: number) => void;
	sectionId: number;
	children: React.ReactNode;
}> = ({ field, index, moveField, sectionId, children }) => {
	const ref = useRef<HTMLDivElement>(null);

	const [{ isDragging }, drag] = useDrag({
		type: ItemTypes.FIELD,
		item: { id: field.id, index, type: ItemTypes.FIELD, sectionId },
		collect: (monitor) => ({
			isDragging: monitor.isDragging(),
		}),
	});

	const [, drop] = useDrop({
		accept: ItemTypes.FIELD,
		hover: (item: DragItem, monitor) => {
			if (!ref.current) return;
			if (item.type !== ItemTypes.FIELD) return;
			if (item.sectionId !== sectionId) return;

			const dragIndex = item.index;
			const hoverIndex = index;

			if (dragIndex === hoverIndex) return;

			const hoverBoundingRect = ref.current?.getBoundingClientRect();
			const hoverMiddleY =
				(hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
			const clientOffset = monitor.getClientOffset();
			const hoverClientY = clientOffset ? clientOffset.y - hoverBoundingRect.top : 0;

			if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
			if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

			moveField(dragIndex, hoverIndex, sectionId);
			item.index = hoverIndex;
		},
	});

	drag(drop(ref));

	return (
		<div
			ref={ref}
			style={{
				opacity: isDragging ? 0.5 : 1,
				cursor: "grab",
				transition: "transform 0.2s ease",
				transform: isDragging ? "scale(1.02)" : "scale(1)",
			}}
		>
			{children}
		</div>
	);
};

const FormBuilder: React.FC<FormBuilderProps> = ({
	sections,
	fields,
	// formData,
	// handleChange,
	handleEditClick,
	handleDelete,
	handleSectionEdit,
	handleSectionDelete,
	handleSectionClick,
	activeSectionId,
	moveField,
	moveSection,
}) => {
	
	return (
		<>
		<DndProvider backend={HTML5Backend}>
			<Box sx={{ padding: 2, height: "75vh", overflow: "auto" }}>
				{sections.length === 0 && (
					<Typography>
						No sections added. Use the sidebar to create a section.
					</Typography>
				)}

				{sections.map((section, index) => (
					<DraggableSection
						key={section.id}
						section={section}
						index={index}
						moveSection={moveSection}
					>
						
						<Paper
							sx={{
								padding: 2,
								marginBottom: 2,
								borderRadius: "6px",
								boxShadow: "0px 4px 10px rgba(0,0,0,0.1)",
								border:
									activeSectionId === section.id
										? "1px solid #F06D1A"
										: "1px solid #ddd",
								backgroundColor: "#ffffff",
								cursor: "pointer",
							}}
							onClick={() => handleSectionClick(section.id)}
						>
							<div className="flex items-center justify-between mb-4">
						<Image
							src={rowicon}
							alt="Row Icon"
							width={20}
							height={20}
							style={{
								marginRight: 10,
								opacity: 0.6,
							}}
						/>
								<Typography variant="h6" className="font-bold">
									{section.label}
								</Typography>
								<div className="flex items-center space-x-1">
									<IconButton
										onClick={(e) => {
											e.stopPropagation();
											handleSectionEdit(section);
										}}
									>
										<EditIcon style={{ fontSize: "18px" }} />
									</IconButton>
									<IconButton
										onClick={(e) => {
											e.stopPropagation();
											handleSectionDelete(section.id);
										}}
										className="text-gray-500"
										disabled={section?.is_default}
									>
										<DeleteIcon style={{ fontSize: "18px" }} />
									</IconButton>
								</div>
							</div>
							<div className="grid grid-cols-12 gap-2">
								{fields
									.filter((field) => field.section_id === section.id)
									.sort(
										(a, b) =>
											(a?.position || 0) - (b?.position || 0),
									)
									.map((field, index) => (
										<div key={field.id} className="col-span-12">
											<DraggableField
												field={field}
												index={index}
												moveField={moveField}
												sectionId={section.id}
											>
												<Box className="flex items-center justify-between border border-gray-300 bg-white rounded-lg p-2 mb-2 hover:shadow-sm transition">
											
													<Box className="flex items-center gap-3">
												<Image
													src={rowicon}
													alt="Row Icon"
													width={20}
													height={20}
													style={{
														marginRight: 10,
														opacity: 0.6,
													}}
												/>
														<Box>
															<Typography className="text-xs text-gray-500">
																{field?.label}
															</Typography>
														</Box>
													</Box>
													<Box className="flex items-center gap-2">
														<IconButton
															onClick={() => handleEditClick(field)}
															size="small"
														>
															<EditIcon style={{ fontSize: "18px" }} />
														</IconButton>
														<IconButton
															onClick={() => handleDelete(field.id)}
															size="small"
															// disabled={field?.is_default}
														>
															<DeleteIcon style={{ fontSize: "18px" }} />
														</IconButton>
													</Box>
												</Box>
											</DraggableField>
										</div>
									))}
							</div>
						</Paper>
					</DraggableSection>
				))}
			</Box>
		</DndProvider>

	  </>
		
	);
};

export default FormBuilder;
