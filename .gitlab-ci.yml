stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE: nagagogulan/aimx-frontend-admin

build:
  stage: build
  tags:
    - docker
  image:
    name: gcr.io/kaniko-project/executor:v1.23.0-debug
    entrypoint: [""]
  script:
    - echo "Building Docker image using Kaniko"
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"https://index.docker.io/v1/\":{\"auth\":\"$(printf "%s:%s" "${DOCKER_USERNAME}" "${DOCKER_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${DOCKER_IMAGE}:${CI_COMMIT_SHORT_SHA}"
      --destination "${DOCKER_IMAGE}:latest"
      --cache=true
      --cache-copy-layers=true
      --cache-ttl=24h
      --single-snapshot
      --cleanup
      --log-format=text
      --verbosity=info
  timeout: 45m
  artifacts:
    reports:
      dotenv: build.env
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  only:
    - feature_initial_commit

deploy:
  stage: deploy
  tags:
    - docker
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client curl
    - eval $(ssh-agent -s)
    - echo "$EC2_SSH_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H "$EC2_HOST" >> ~/.ssh/known_hosts
  script:
    - echo "Deploying to EC2 server"
    - |
      ssh -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST "
        echo 'Logging into Docker Hub...' &&
        echo '$DOCKER_PASSWORD' | docker login --username '$DOCKER_USERNAME' --password-stdin &&
        echo 'Pulling latest image...' &&
        docker pull $DOCKER_IMAGE:latest &&
        echo 'Stopping existing container...' &&
        (docker stop app || echo 'No container named app running') &&
        (docker rm app || echo 'No container named app to remove') &&
        echo 'Starting new container...' &&
        docker run -d --name app -p 3000:3000 --restart unless-stopped $DOCKER_IMAGE:latest &&
        echo 'Deployment completed successfully!' &&
        docker ps | grep app
      "
  timeout: 10m
  retry:
    max: 1
    when:
      - runner_system_failure
  only:
    - feature_initial_commit