// components/OrganizationSection.tsx
'use client';

import React from 'react';
import { Box, Typography, Avatar } from '@mui/material';


// const organizations = [
//   {
//     name: 'Apex Edge Nanotech',
//     email: '<EMAIL>',
//     date: 'Apr 1, 2022',
//     time: '2:00 PM',
//     avatar: '',
//   },
//   {
//     name: 'Nvidia',
//     email: 'jane<PERSON>@nvidia.com',
//     date: 'Apr 2, 2022',
//     time: '4:30 PM',
//     avatar: '',
//   },
//   {
//     name: 'Alphabet',
//     email: '<EMAIL>',
//     date: 'Apr 3, 2022',
//     time: '10:45 PM',
//     avatar: '',
//   },
// ];

const OrganizationSection = ({ organization }) => {
  console.log("organization", organization)
  return (
    <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto">
      {/* Header */}
      <Box className="flex justify-between items-center px-5 py-5 border-b border-[#d7dade] min-w-[500px]">
        <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-black text-base font-bold whitespace-nowrap">
          Organizations
        </Typography>
        <Typography className="text-xs text-[#004fb0] font-bold cursor-pointer whitespace-nowrap" >
          View All
        </Typography>
      </Box>

      {/* Table Headers */}
      <Box className="flex items-center justify-between px-4 py-3 border-b border-[#d7dade] min-w-[500px]">
        {/* Left: Organization Name */}
        <Typography sx={{ fontWeight: 'bold', fontSize: "16px" }} className="text-[#161616] font-bold whitespace-nowrap ml-2">
          Organization Name
        </Typography>

        {/* Right: Joined At */}
        <Typography sx={{ fontWeight: 'bold', fontSize: "16px" }} className="text-[#161616] font-bold whitespace-nowrap mr-2">
          Joined At
        </Typography>
      </Box>

      {/* Organization Rows */}
      <Box className="min-w-[500px]">
        {organization?.length !== 0 && organization.map((org, i) => (
          <Box
            key={org.email}
            className={`flex items-center justify-between px-4 py-4 gap-x-5 ${i !== 0 ? 'border-t border-gray-100' : ''}`}
          >
            {/* Left: Organization Info */}
            <Box className="flex items-center gap-3">

              <Avatar
                alt={org.name || org.email}
                src=""
                sx={{ width: 40, height: 40, bgcolor: '#FF6900' }}
              >
                {/* {(org?.name || org.email).charAt(0).toUpperCase()} */}
              </Avatar>
              <Box>
                <Typography sx={{ fontSize: "16px", fontWeight: "600" }} className="text-gray-800 font-inter">
                  {org.name}
                </Typography>
                <Typography sx={{ fontSize: "15px" }} className="text-xs text-gray-500 break-all">
                  {org.email}
                </Typography>
              </Box>
            </Box>

            {/* Right: Joined At */}
            <Box className="text-right mr-2">
              <Typography sx={{ fontSize: "16px", fontWeight: "600" }} className="text-sm font-semibold text-gray-800 whitespace-nowrap">
                {org.createdAt}
              </Typography>
              {/* <Typography sx={{ fontSize: "15px" }} className="text-xs text-gray-500 whitespace-nowrap">
                {org.time}
              </Typography> */}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>

  );
};

export default OrganizationSection;
