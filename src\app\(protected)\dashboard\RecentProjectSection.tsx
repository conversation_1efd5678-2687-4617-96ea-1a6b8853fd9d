'use client';
 
import React from 'react';
import { Box, Typography, Avatar, Tooltip } from '@mui/material';
import { useRouter } from "next/navigation";
 
interface Project {
  name: string;
  status: string;
  date: string;
  time: string;
  Speciality: string;
  Description: string
}
 
 
 
 
const RecentProjectsSection = ({ projects }: { projects: Project[] }) => {
  const router = useRouter();
 
  return (
 
    <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto sm:w-auto">
      <Box className="flex justify-between items-center  px-5 py-5  border-b border-[#d7dade] min-w-[900px]">
        <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-black text-base font-bold whitespace-nowrap">
          Recent Datasets
        </Typography>
        <Typography
          className="text-xs text-[#004fb0] font-bold cursor-pointer whitespace-nowrap"
          onClick={() => router.push("/datasets")}
        >
          View All
        </Typography>
      </Box>
 
      {projects?.length !== 0 ? (
        <>
          <Box className="grid grid-cols-[2fr_2fr_1fr_1.5fr] items-center px-5 py-3 border-b border-[#d7dade] min-w-[900px] bg-white">
            <Box>
              <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-[#161616] text-base font-bold whitespace-nowrap">
               Datasets
              </Typography>
            </Box>
            <Box>
              <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-[#161616] text-base font-bold whitespace-nowrap">
                Description
              </Typography>
            </Box>
            <Box>
              <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-[#161616] text-base font-bold whitespace-nowrap">
                Speciality
              </Typography>
            </Box>
 
            <Box sx={{ display: 'flex', justifyContent: 'end' }}>
              <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-[#161616] text-base font-bold whitespace-nowrap">
                Uploaded on
              </Typography>
            </Box>
          </Box>
 
 
          <Box className="min-w-[900px]">
            {projects.slice(0, 3).map((project, i) => (
              <Box
                key={project.name}
                className={`grid grid-cols-[2fr_2fr_1fr_1.5fr] items-center px-5 py-3 gap-x-4 ${i !== 0 ? 'border-t border-gray-100' : ''}`}
              >
                <Box className="flex items-center gap-3">
                  <Avatar alt={project.name} sx={{ bgcolor: '#FF6900', width: 40, height: 40 }}>
                    {(project.name || 'N').trim().charAt(0).toUpperCase()}
                  </Avatar>
                  <Typography className="text-sm text-black font-medium break-words">
                    {project.name.charAt(0).toUpperCase() + project.name.slice(1)}
                  </Typography>
                </Box>
 
                <Tooltip title={project.Description} placement="top" arrow>
                  <Typography className="text-sm text-black whitespace-nowrap truncate max-w-[250px]" style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {project.Description}
                  </Typography>
                </Tooltip>
 
                <Typography className="text-sm text-black whitespace-nowrap">
                  {project.Speciality}
                </Typography>
 
 
                <Box className="flex flex-col whitespace-nowrap">
                 <Typography sx={{ fontSize: "16px", fontWeight: "600", display: 'flex', justifyContent: 'end'  }} className="text-sm font-semibold text-gray-800 whitespace-nowrap">
                    {(() => {
                      const [day, month, year] = project.date.split('/').map(Number);
                      const correctDate = new Date(year, month - 1, day);
                      return correctDate.toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      });
                    })()}
 
                  </Typography>
 
                  <Typography  sx={{  display: 'flex', justifyContent: 'end'  }} className="text-xs text-gray-500">
                    {project.time}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </>
      ) : (
        <Box className="grid grid-cols-3 items-center px-5 py-4 min-w-[600px]">
          <p className="text-gray-500 text-center col-span-3 w-full">No projects found.</p>
        </Box>
      )}
    </Box>
  );
};
 
export default RecentProjectsSection;