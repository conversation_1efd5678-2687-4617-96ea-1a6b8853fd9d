import {
	Box,
	<PERSON><PERSON>,
	Tabs,
	Tab,
	Typography,
	// TextField,
	// Radio,
	// RadioGroup,
	// FormControlLabel,
	// InputLabel,
	useMediaQuery,
	useTheme,
	Drawer,
} from "@mui/material";
import { useState } from "react";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import Image from "next/image";
import signinother from "../../../../assests/filuploadbg.png";
import { docketUpload } from "../../../../services/uploadApiService";
import { toast } from "react-toastify";

interface UploadDrawerFormProps {
	setOpen: (open: boolean) => void;
	onFileUpload: (data: { file_path: string; id: string }) => void;
	onContinue: () => void;
}

type TabChangeEvent = React.SyntheticEvent & {
	target: EventTarget & {
		value: number;
	};
};

export default function UploadDrawerForm({
	setOpen,
	onFileUpload,
	onContinue,
}: UploadDrawerFormProps) {
	const theme = useTheme();
	const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
	// const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
	const [loading, setLoading] = useState(false);
	const [activeTab, setActiveTab] = useState<number>(0);
	const [selectedRepo, setSelectedRepo] = useState<string>("github");
	const [url, setUrl] = useState<string>("");
	const [pat, setPat] = useState<string>("");
	const [uploadedFiles, setUploadedFiles] = useState<
		Record<string, { file_path: string; id: string }>
	>({});

	const handleCloseDrawer = (): void => {
		setOpen(false);
		setActiveTab(0);
		setSelectedRepo("github");
		setUrl("");
		setPat("");
	};

	const handleTabChange = (event: TabChangeEvent, newValue: number): void =>
		setActiveTab(newValue);

	const handleFileUpload = async (file: File) => {
		const fieldId = "upload_file";

		if (uploadedFiles[fieldId]) {
			await handleFileDelete(fieldId);
		}

		try {
			const formData = new FormData();
			formData.append("fileName", file);

			setLoading(true);
			const uploadRes = await docketUpload(formData);
			setLoading(false);
			const fullPath = uploadRes?.data?.file_path || file.name;
			const fileId = uploadRes?.data?.id;
			const filePath = fullPath.replace(/^Documents\//, "");
			const fileObj = { file_path: filePath, id: fileId };

			setUploadedFiles((prev) => ({
				...prev,
				[fieldId]: fileObj,
			}));
		} catch (error) {
			console.error("File upload failed:", error);
			setLoading(false);
		}
	};

	const handleFileDelete = async (fieldId: string) => {
		const existingFile = uploadedFiles[fieldId];
		if (!existingFile) return;
try{

		setLoading(true);
		setUploadedFiles((prev) => {
			const updated = { ...prev };
			delete updated[fieldId];
			return updated;
		});
	} catch (error) {
		console.error("File deletion failed:", error);
	}finally{
		setLoading(false);
	}
	}

	const handleContinue = () => {
		if (activeTab === 0 && uploadedFiles["upload_file"]) {
			// For file upload
			onFileUpload(uploadedFiles["upload_file"]);
		} else if (activeTab === 1 && url && pat) {
			// For repository link
			const repoData = {
				file_path: `${selectedRepo}_repository`,
				id: Date.now().toString(),
				link: url,
				pat: pat
			};
			
			console.log("Sending repository data to parent:", repoData); // Debug log
			onFileUpload(repoData);
		} else {
			toast.error("Please upload a file.");
			return;
		}

		onContinue();
		handleCloseDrawer();
	};


	const handleFileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		e.target.value = "";	 // Reset the input value to allow re-uploading the same file
		if (file) {
			const allowedExtensions = ["csv", "xlsx", "zip"];
			const fileExtension = file.name.split(".").pop()?.toLowerCase();

			if (fileExtension && allowedExtensions.includes(fileExtension)) {
				await handleFileUpload(file);
			} else {
				toast.error("Invalid file type. Only .csv, .xlsx, and .zip are allowed.");
			}
		}
	};

	return (
		<>
		<Drawer
				open={true}
				onClose={handleCloseDrawer}
				anchor="right"
				PaperProps={{
					sx: { width: { xs: "100%", md: 700, lg: 700 } },
					className: "bg-white shadow-lg",
				}}
			>
			<Box sx={{ padding: isMobile ? 2 : 3 }}>
				<Typography variant="h6" mb={2} sx={{ fontWeight: "bold", fontSize: isMobile ? "1.1rem" : "1.25rem" }}>
					Upload Dataset
				</Typography>

				<Tabs
					value={activeTab}
					onChange={handleTabChange}
					sx={{ mt: 2, mb: 2 ,width: isMobile ? "auto" : "100%",
						minWidth: 0,}}
					variant= "standard"
					TabIndicatorProps={{
						style: {
							backgroundColor: "gray",
							height: "2px",
						},
					}}
				>
						<Tab label="File" className="text-transform-none" sx={{ color: "black !important", fontWeight: "bold", fontSize: "1rem", outline: "none", "&.Mui-selected": { backgroundColor: "#FEF4ED", borderRadius: "8px", color: "black !important" } }} />
					{/* <Tab label="Link" sx={{ fontWeight: "bold" }} /> */}
				</Tabs>

				{activeTab === 0 && (
					<Box
						sx={{
							position: "relative",
							width: "100%",
							height: isMobile ? "calc(90vh - 150px)" : "calc(90vh - 150px)",
							borderRadius: 2
						}}
					>
						<Image src={signinother} alt="Form Background" layout="fill" objectFit="cover" className="z-0" />
                      
						<Box
							onDragOver={(e) => e.preventDefault()}
							onDrop={async (e) => {
								e.preventDefault();
								const file = e.dataTransfer.files?.[0];
								if (!file) return;

								const allowedExtensions = /\.(csv|xlsx|zip)$/i;
								if (allowedExtensions.test(file.name)) {
									await handleFileUpload(file);
								} else {
									toast.error("Invalid file type. Only .csv, .xlsx, and .zip are allowed.");
								}
							}}
							sx={{
								position: "relative",
								zIndex: 10,
								height: "100%",
								width: "100%",
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								border: "2px dashed #F06D1A",
								backgroundColor: "rgba(255,255,255,0.85)",
								borderRadius: 2,
								padding: isMobile ? 2 : 3
							}}
						>
							<CloudUploadIcon sx={{ fontSize: isMobile ? 40 : 50, color: "#F06D1A" }} />
							<Typography mt={2} sx={{ fontWeight: "600", fontSize: "16px", lineHeight: "24px", fontFamily: '"Open Sans", sans-serif', }}>
								Start by uploading a file
							</Typography>
							<Typography
								variant="body2"
								color="textSecondary"
								mt={1}
								sx={{ fontWeight: 400, color: "#475467", fontSize: "14px", lineHeight: "20px", fontFamily: '"Open Sans", sans-serif', }}
							>
								Start creating by uploading your Docket.
							</Typography>
							<Typography
								variant="body2"
								color="textSecondary"
								mt={0}
								sx={{ fontWeight: 400, color: "#475467", fontSize: "14px", fontFamily: '"Open Sans", sans-serif', }}
							>
								Accepted formats: .csv, xlsx, zip
							</Typography>
							<Button
								component="label"
								variant="contained"
								disabled={!!url || loading} 
								className="text-transform-none"
										startIcon={
											<svg
												width="25"
												height="25"
												viewBox="0 0 24 24"
												fill="none"
												xmlns="http://www.w3.org/2000/svg"
											>
												<path
													d="M12 21V11M12 11L9 14M12 11L15 14M7 16.8184C4.69636 16.2074 3 14.1246 3 11.6493C3 9.20008 4.8 6.9375 7.5 6.5C8.34694 4.48637 10.3514 3 12.6893 3C15.684 3 18.1317 5.32251 18.3 8.25C19.8893 8.94488 21 10.6503 21 12.4969C21 14.8148 19.25 16.7236 17 16.9725"
													stroke={loading ? "#C5C5C5" : "#FFFFFF"}
													strokeWidth="2"
													strokeLinecap="round"
													strokeLinejoin="round"
												/>
											</svg>
										}
								sx={{
									mt: 3,
									background: loading ? "#C5C5C5" : "linear-gradient(to right, #F45C24, #FFCB80)",
									borderRadius: "5px",
									px: isMobile ? 3 : 5,
									py: isMobile ? 1 : 1.5,
									fontWeight: "bold",
									fontSize: isMobile ? "0.8rem" : "0.875rem",
									height: "40px",
									width: { md: "352px", sm: "350px" },
									whiteSpace: "nowrap",
								}}
							>
								Drag & Drop or Upload
								<input type="file" accept=".csv,.xlsx,.zip" hidden onChange={handleFileInputChange} />
							</Button>
							{loading && <div >
								<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mt-4" />
							</div>}
							

							

							{uploadedFiles["upload_file"] && (
								<Box 		
								sx={{
											mt: 1,
											p: 2,
											border: "1px solid #ddd",
											borderRadius: 2,
											backgroundColor: "#fafafa",
											display: "flex",
											alignItems: "center",
											justifyContent: "space-between",
											width: "100%",
											maxWidth: 400,
										}}>
									<Typography variant="body1" sx={{ overflowWrap: "anywhere" }}>
										{uploadedFiles.upload_file?.file_path.length > 30 ? `${uploadedFiles.upload_file.file_path.slice(0, 30)}...` : uploadedFiles.upload_file?.file_path}
									</Typography>
									<Button onClick={() => handleFileDelete("upload_file")} sx={{ minWidth: 0, color: "red", fontWeight: "bold", ml: 2 }}>✕</Button>
								</Box>
							)}
							
						</Box>
					</Box>
				)}

				{/* {activeTab === 1 && (
					<Box mt={3}>
						<RadioGroup value={selectedRepo} onChange={(e) => setSelectedRepo(e.target.value)}>
							{["github", "huggingface"].map((repo) => (
								<Box
									key={repo}
									sx={{ border: selectedRepo === repo ? "2px solid orange" : "1px solid #ccc", borderRadius: 2, p: 2, mb: 2, opacity: uploadedFiles["upload_file"] ? 0.5 : 1, pointerEvents: uploadedFiles["upload_file"] ? "none" : "auto" }}
								>
									<FormControlLabel
										value={repo}
										control={<Radio />}
										label={<Box><Typography fontWeight="bold">Import {repo} repository</Typography><Typography variant="body2">Create from a {repo} repository archive.</Typography></Box>}
									/>
								</Box>
							))}
						</RadioGroup>
						<InputLabel>Remote URL</InputLabel>
						<TextField placeholder="Enter remote URL" sx={{ marginBottom: "10px" }} value={url} onChange={(e) => setUrl(e.target.value)} fullWidth margin="normal" disabled={!!uploadedFiles["upload_file"]} />
						<InputLabel>PAT Token</InputLabel>
						<TextField placeholder="Enter PAT token" value={pat} onChange={(e) => setPat(e.target.value)} fullWidth margin="normal" disabled={!!uploadedFiles["upload_file"]} />
					</Box>
				)} */}
			</Box>

			<Box sx={{ p: 1, display: "flex", justifyContent: "space-evenly", borderTop: "1px solid #eee"}}>
				<Button variant="outlined" onClick={handleCloseDrawer} sx={{ fontWeight: "bold", width: "150px", color: "#F06D1A", border: "1px solid #F06D1A" }}>
					Back
				</Button>
				<Button onClick={handleContinue} variant="contained" sx={{ background: "linear-gradient(to right, #F45C24, #FFCB80)", borderRadius: "5px", px: 5, fontWeight: "bold" }}>
					Continue
				</Button>
			</Box>
		</Drawer>
			
		</>
	);
}
