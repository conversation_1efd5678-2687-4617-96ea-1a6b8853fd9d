import React from "react";

interface ActionCardProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  iconBgColor: string;
  onClick?: () => void; // <-- allow onClick prop
}

const ActionCard: React.FC<ActionCardProps> = ({
  title,
  subtitle,
  icon,
  iconBgColor,
  onClick,
}) => {
  return (
    <div
      onClick={onClick}
      className="flex items-center justify-between p-6 border border-gray-200 rounded-lg shadow-sm bg-white cursor-pointer hover:shadow-md transition"
    >
      <div className="flex-1 pr-4">
        <h3 className="font-semibold text-lg text-gray-900">{title}</h3>
        <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
      </div>
      <div
        className={`${iconBgColor} p-3 rounded-full w-12 h-12 flex items-center justify-center`}
      >
        {icon}
      </div>
    </div>
  );
};

export default ActionCard;
