import { Box } from "@mui/material";

interface AvatarFallbackProps {
  name?: string; // make optional
  className?: string;
}

const AvatarFallback: React.FC<AvatarFallbackProps> = ({ name = "", className }) => {
  const initials = name
    .split(" ")
    .slice(0, 2)
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase();

  return (
    <Box
      className={`flex items-center justify-center bg-orange-500 text-white font-bold rounded border border-orange-500 ${className}`}
    >
      {initials || "U"} {/* Fallback if name is empty */}
    </Box>
  );
};

export default AvatarFallback;
