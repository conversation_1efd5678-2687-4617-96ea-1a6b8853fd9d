"use client";

interface Section {
    label: string;
    // Add other properties as needed
}
import React from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    TextField,
    InputLabel,
} from "@mui/material";

interface SectionEditorProps {
    open: boolean;
    section: Section | null;
    onClose: () => void;
    onSave: (section: Section) => void;
}

const SectionEditor: React.FC<SectionEditorProps> = ({
    open,
    section,
    onClose,
    onSave,
}) => {
    const [editedSection, setEditedSection] = React.useState<Section | null>(null);

    React.useEffect(() => {
        setEditedSection(section ? { ...section } : null);
    }, [section]);

    if (!editedSection) return null;

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
            <DialogTitle>Edit Section</DialogTitle>
            <DialogContent>
                <>
                <InputLabel>Section Name</InputLabel>
                <TextField
                    autoFocus
                    margin="dense"
                    fullWidth
                    value={editedSection.label}
                    onChange={(e) =>
                        setEditedSection({ ...editedSection, label: e.target.value })
                    }
                /></>
            </DialogContent>
            <DialogActions
                            className="px-6 pb-10 pt-1 flex justify-center gap-4"
                            sx={{
                                justifyContent: "center",
                                paddingBottom: "15px",
                                paddingTop: "0px",
                            }}
                        >
                            <Button
                                variant="outlined"
                                onClick={onClose}
                                style={{
                                    width: "170px",
                                    fontSize: "15px",
                                    borderColor: "#F45C24",
                                    color: "#F45C24",
                                    
                                }}
                                className="sm:w-auto px-6 py-3 normal-case text-transform-none"
                            >
                                Cancel
                            </Button>
                            <Button
                                variant="contained"
                                onClick={() => {
                                    onSave(editedSection);
                                    onClose();
                                }}
                                style={{ width: "170px", fontSize: "15px", textTransform: "none" }}
                                className="sm:w-auto px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case"
                            >
                                Save for Changes
                            </Button>
                        </DialogActions>
        </Dialog>
    );
};

export default SectionEditor;