import qs from 'qs';
import { createApiClient } from '../lib/createApiClient';

const USER_BASE_URL = process.env.NEXT_PUBLIC_USER_BASE_URL;
const userServiceApi = createApiClient(USER_BASE_URL);

export const getUser = async (payload) => {
    const response = await userServiceApi.get("user-profile", {
        params: payload,
        paramsSerializer: (params) => {
          return qs.stringify(params, { encode: false });
        },
      });
      return response;
  };

export const deactivateUser = async (payload) => {
    const response = await userServiceApi.put(`user-profile/deactivate/${payload}`);
    return response;
};


export const activateUser = async (payload) => {
  const response = await userServiceApi.put(`user-profile/activate/${payload}`);
  return response;
};