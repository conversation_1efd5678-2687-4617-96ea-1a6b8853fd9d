"use client";

import { Card, CardContent, Typography, Box } from "@mui/material";
import { CalendarDays, Clock, Archive, Sparkles } from "lucide-react";

interface ProjectData {
  active_count: string;
  archive_count: string;
  pending_count: string;
  rejected_count: string;
}

const ProjectSummaryCards = ({ data }: { data: { project?: ProjectData } | null }) => {
  const project = data?.project;

  const summaryData = [
    {
      count: project?.active_count || "0",
      label: "Active Projects",
      icon: <Sparkles className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.archive_count || "0",
      label: "Archived Projects",
      icon: <Archive className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.pending_count || "0",
      label: "Pending Evaluations",
      icon: <Clock className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.rejected_count || "0",
      label: "Upcoming Deadlines",
      icon: <CalendarDays className="w-8 h-8 text-orange-600" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mt-6">
      {summaryData.map((item, index) => (
        <Card
          key={index}
          className="relative overflow-visible border border-gray-200 shadow-sm rounded-xl min-h-[180px]"
        >
          <CardContent className="p-6 flex flex-col justify-between h-full">
            <div>
              <Typography
                variant="h4"
                className="font-bold text-[2rem] text-blue-700"
              >
                {item.count}
              </Typography>
              <Typography
                variant="h6"
                className="font-semibold text-gray-700 mt-4"
              >
                {item.label}
              </Typography>
            </div>
          </CardContent>
          <Box className="absolute top-[-30px] right-[-30px] bg-orange-100 h-28 w-28 rounded-full flex items-center justify-center shadow-md">
            {item.icon}
          </Box>
        </Card>
      ))}
    </div>
  );
};

export default ProjectSummaryCards;
