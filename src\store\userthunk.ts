import { createAsyncThunk } from "@reduxjs/toolkit";
import { Getrole } from "../services/profileApiService";
import { userResponse } from "../store/userSlice";

export const fetchUserById = createAsyncThunk(
  "user/fetchById",
  async (userId: string, { dispatch, rejectWithValue }) => {
    try {
      const res = await Getrole(userId);
      dispatch(userResponse(res.data));
      return res.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch user");
    }
  }
);