"use client";

import { useState, useEffect, Suspense } from "react";
import Sidebar from "./SideBar";
import Header from "./Header";
import Footer from "./Footer";
import { useMediaQuery, useTheme } from "@mui/material";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));
  const [isOpen, setIsOpen] = useState<boolean>(!isMobile);

  // Close sidebar when screen size changes to mobile
  useEffect(() => {
    if (isMobile) {
      setIsOpen(false);
    } else {
      setIsOpen(true);
    }
  }, [isMobile]);

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50">
      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full z-30 transition-transform duration-300
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
          ${isMobile ? "w-64" : isTablet ? "w-56" : "w-64"}
          md:translate-x-0`}
      >
        <Suspense fallback={<div className="flex items-center justify-center h-full">Loading...</div>}>
          <Sidebar isOpen={isOpen} setIsOpen={setIsOpen} />
        </Suspense>
      </div>

      {/* Overlay for mobile */}
      {isOpen && isMobile && (
        <div
          className="fixed inset-0 bg-white/30 backdrop-blur-sm z-20"
          onClick={() => setIsOpen(false)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              setIsOpen(false);
            }
          }}
          role="button"
          tabIndex={0}
        />
      )}

      {/* Main Content */}
      <div
        className={`flex flex-col flex-1 min-w-0 transition-all duration-300
          ${isOpen && !isMobile ? (isTablet ? "ml-56" : "ml-64") : !isOpen && !isMobile ? "ml-20" : "ml-0"}`}
      >
        {/* Header */}
        <header
          className={`fixed top-0 z-10 transition-all duration-300
            ${isOpen && !isMobile ? (isTablet ? "w-[calc(100%-14rem)] left-56" : "w-[calc(100%-16rem)] left-64") : "w-full left-0"}`}
        >
          <Header onMenuToggle={() => setIsOpen((prev) => !prev)} />
        </header>

        {/* Scrollable Main Area */}
        <main
          className={`flex-1 overflow-y-auto mt-16 p-4 
            ${isMobile ? "px-2" : isTablet ? "px-4" : "px-6"}`}
        >
          {children}
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default Layout;
