import CryptoJS from 'crypto-js';

// Encryption function
export const encryptData = (data: unknown, passphrase: string): string => {
  if (!data) throw new Error('No data provided for encryption');
  if (!passphrase) throw new Error('No passphrase provided');
  
  try {
    return CryptoJS.AES.encrypt(
      JSON.stringify(data),
      passphrase
    ).toString();
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

// Decryption function
export const decryptData = (ciphertext: string, passphrase: string): unknown => {
  if (!ciphertext) throw new Error('No ciphertext provided');
  if (!passphrase) throw new Error('No passphrase provided');
  
  try {
    const bytes = CryptoJS.AES.decrypt(ciphertext, passphrase);
    const decrypted = bytes.toString(CryptoJS.enc.Utf8);
    
    if (!decrypted) {
      throw new Error('Failed to decrypt or empty result');
    }
    
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};