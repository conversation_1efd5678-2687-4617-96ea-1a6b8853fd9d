  .css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
  }
  .css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
    color: #292929 !important;
  }
  .css-z8nmqa-MuiSvgIcon-root {
    fill: #F06D1A !important;
  }
  .css-1bz1rr0-MuiSvgIcon-root {
  fill:#F06D1A !important;
  }
  .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track {
    background-color: #F06D1A !important;
  }
  .css-161ms7l-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked {
    color: #F06D1A !important;
  }
  .css-1dune0f-MuiInputBase-input-MuiOutlinedInput-input {
    padding: 13px 14px !important;
  }
  /* .css-1umw9bq-MuiSvgIcon-root {
    fill:#F06D1A !important;
  } */
  .css-16bevx5-MuiFormControl-root-MuiTextField-root{
    margin-top: 10px !important;
    margin-bottom: 0px !important;
  }
  .css-16mfwdg-MuiFormControl-root{
    margin-top: 5px !important;
    margin-bottom: 0px !important;
  }
  .select-lable{
    color: #696666 !important;
  }
  .MuiOutlinedInput-root:not(.Mui-focused):hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23) !important; /* Default border color */
  }
  .prebutton{
    padding: 10px !important;
  }
  /* In your global CSS file */
.form-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 16px;
  width: 100%;
  padding: 1rem 2rem;
}
.css-nwsdb5-MuiFormLabel-root.Mui-focused {
  color: #6b6b6b !important;
}

.grid-col-4 {
  grid-column: span 4 / span 4;
}

.grid-col-6 {
  grid-column: span 6 / span 6;
}

.grid-col-12 {
  grid-column: span 12 / span 12;
}
.css-1tlcqt-MuiAutocomplete-root .MuiOutlinedInput-root {
    padding: 0px !important;
}