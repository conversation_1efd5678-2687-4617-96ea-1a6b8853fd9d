import type React from "react";
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from "@mui/material";

interface ConfirmDialogProps {
  open: boolean;
  title?: string;
  description: string;
  onConfirm: () => void;
  onClose: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, title = "Confirmation", description, onConfirm, onClose }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
      <DialogContent className="text-sm text-gray-700 py-2">{description}</DialogContent>
      <DialogActions className="px-4 pb-3">
        <Button onClick={onClose} className="text-gray-600">Cancel</Button>
        <Button onClick={onConfirm} variant="contained" className="bg-orange-500 text-white hover:bg-orange-600">Ok</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDialog;
