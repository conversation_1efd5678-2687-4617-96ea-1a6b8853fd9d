"use client";

import type React from "react";
import { useState, useEffect } from "react";
import DatasetCard from "../../../components/common/DatasetCard";
import SearchFilter from "../../../components/ui/SearchFilter";
import FilterSidebar from "../../../components/ui/FilterSidebar";
import { organizationList } from "../../../services/desApiService";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "../../../store/store";
import { setDatasetList, setDatasetLoading } from "../../../store/datasetSlice";
import TablePagination from "../../../components/ui/Pagination";
import { useModulePermissions } from "../../../hooks/useModulePermissions";
import CustomButton from "../../../components/ui/Button";
import { useRouter } from "next/navigation";


interface Paging {
  total_page: number;
  current_page: number;
  item_per_page: number;
  total_items: number;
}

const DatasetsPage: React.FC = () => {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [datasetListData, setDatasetListData] = useState<[]>([]);
  const [datasetPageInfo, SetDatasetpageInfo] = useState<{
    pagingInfo?: Paging;
  }>({});
  const [appliedFilters, setAppliedFilters] = useState<
    { fields: string; value: string }[]
  >([]);
  const [queryStrings, setQueryString] = useState({
    page: 1,
    pageSize: 10,
    type: 2,
    formname: ' ',
  });

  const dispatch = useDispatch();
  const {canRead} = useModulePermissions("Datasets");
  
  const loading = useSelector((state: RootState) => state.dataset.loading);
  useEffect(() => {
  
    fetchFormData();
  }, [queryStrings, dispatch]);

  const fetchFormData = async () => {

    dispatch(setDatasetLoading(true));
    try {
      const params = {
        ...queryStrings,
        filter:
          appliedFilters.length > 0
            ? JSON.stringify(appliedFilters)
            : undefined,
      };
      const datasetFormvalue = await organizationList(params);
      dispatch(setDatasetList(datasetFormvalue?.data?.formdtoData || []));
      setDatasetListData(datasetFormvalue?.data?.formdtoData || []);
      SetDatasetpageInfo(datasetFormvalue?.data);
    } catch (err) {
      console.error("Failed to fetch form data", err);
    } finally {
      dispatch(setDatasetLoading(false));
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setQueryString(prev => ({
      ...prev,
      page: 1,
      formname: query,
    }));
  };
  const handleFilterApply = (
    appliedFilters: { fields: string; value: string }[],
  ) => {
    setAppliedFilters(appliedFilters);
    setQueryString((prev) => ({ ...prev, page: 1 }));
  };
    const handleUploadDataset = () => {
    router.push("/datasets/create");
  };

  return (
    <>
      {/* {loading && <Loader />} */}
      <FilterSidebar initialFilters={appliedFilters} type={Number(2)} onFilterApply={handleFilterApply} open={isFilterOpen} onClose={() => setIsFilterOpen(false)} />
      <div className="bg-white  p-6  min-h-screen" >
        <div className="flex justify-between items-center mb-4">
                  <h1 className="text-2xl font-bold mb-4">Datasets</h1>
                    <CustomButton
                    text="Upload Dataset"
                    onClick={handleUploadDataset}
                    isLoading={false}
                  />
                </div>
        <SearchFilter
          searchQuery={searchQuery}
          setSearchQuery={handleSearch}
          onFilterClick={() => setIsFilterOpen(true)}
          placeholder="Search Datasets"
        />

        <div className="space-y-4 overflow-y-auto mt-4" style={{ height: "39rem" }}>
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500" />
            </div>
          ) :datasetListData.length > 0 ? (
            datasetListData.map((dataset: { id: string }) => (
              <DatasetCard key={dataset.id} dataset={dataset} canRead={canRead} datasetApi={fetchFormData} />
            ))
          ) : (
            <div className="text-center text-gray-500 text-sm mt-10">
              No records found
            </div>
          )}
        </div>
        <TablePagination
          queryStrings={queryStrings}
          setQueryString={setQueryString}
          paging={datasetPageInfo?.pagingInfo}
        />
      </div>
    </>
  );
};

export default DatasetsPage;