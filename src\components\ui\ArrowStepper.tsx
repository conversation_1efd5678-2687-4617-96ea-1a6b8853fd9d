import { ChevronUp, ChevronDown } from "lucide-react";
import { Box, IconButton } from "@mui/material";

const ArrowStepper = ({onChange }) => {
  const handleIncrement = () => {
    onChange((prev) => Number(prev || 0) + 1);
  };

  const handleDecrement = () => {
    onChange((prev) => Math.max(0, Number(prev || 0) - 1)); // avoids going below 0
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "32px",
        justifyContent: "center",
      }}
    >
      <IconButton size="small" sx={{ padding: "2px" }} onClick={handleIncrement}>
        <ChevronUp size={18} />
      </IconButton>
      <IconButton size="small" sx={{ padding: "2px" }} onClick={handleDecrement}>
        <ChevronDown size={18} />
      </IconButton>
    </Box>
  );
};

export default ArrowStepper;
