"use client";

import type React from "react";
import { useState, useEffect } from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	Drawer,
	Button,
	Checkbox,
	FormControlLabel,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import ApprovalModal from "../../../components/requestManagement/ApprovalModal";
import {
	getDropdownRequest,
	getRequestList,
} from "../../../services/requestapiService";
import SearchFilter from "../../../components/ui/SearchFilter";
import { tailwindStyles } from "../../../styles/tailwindStyles";
import { requestTableHeaders } from "../../../components/ui/tableHeaders";
import RequestManagementFilter from "../../../components/ui/RequestApprovalFilter";
import CustomButton from "../../../components/ui/Button";
import ApprovalRequestModal from "../../../components/requestManagement/ApprovalRequestModal";
import { admin, formatDate, superadmin } from "../../../constants/fieldtype";
import type { RootState } from "../../../store/store";
import { useAppSelector } from "../../../store/store";
import TablePagination from "../../../components/ui/Pagination";

interface Paging {
	total_page: number;
	current_page: number;
	item_per_page: number;
	total_items: number;
}

interface Role {
	description: string;
	id: string;
	name: string;
}

interface UserData {
	id: string | number;
	organization_name?: string;
	request_type_label?: string;
	created_at?: string;
	status?: string;
	status_label?: string;
	role?: string;
	value?: string;
	request_type?: string;
	commands_by_organization?: string;
	[key: string]: string | number | undefined;
}

interface AppliedFilters {
	request_type?: number;
	status?: number;
	[key: string]: string | number | undefined;
}

const RequestApprovalPage: React.FC = () => {
	const [searchQuery, setSearchQuery] = useState("");
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [columnDrawerOpen, setColumnDrawerOpen] = useState(false);
	const [visibleExtraFields, setVisibleExtraFields] = useState<string[]>([]);
	const [dropdownList, setDropdownList] = useState([]);
	const [isModalOpen, setIsModalOpen] = useState({
		approval: false,
		request: false,
		reqdata: undefined,
		actiontype: "",
	});
	const [appliedFilters, setAppliedFilters] = useState<AppliedFilters>({});

	const role = useAppSelector((state: RootState) => state.user?.userDetail?.role,) as unknown as Role;
	const userDetail = useAppSelector((state: RootState) => state?.user?.userDetail,);
	

	const [queryStrings, setQueryString] = useState({
		page: 1,
		pageSize: 10,
		search: "",
	});

	const [requestData, setRequestData] = useState({
		requests: [] as UserData[],
		pagingInfo: {
			total_page: 0,
			current_page: 1,
			item_per_page: 10,
			total_items: 0,
		} as Paging,
	});

	const defaultHeaders = requestTableHeaders.slice(0, 6);
	const optionalHeaders = requestTableHeaders.slice(6);

	useEffect(() => {
		fetchDropDownRequests();
	}, []);

	useEffect(() => {
		const timer = setTimeout(() => {
			fetchRequests();
		}, 300);

		return () => clearTimeout(timer);
	}, [queryStrings, appliedFilters]);

	const fetchDropDownRequests = async () => {
		try {
			const response = await getDropdownRequest();
			if (response?.data) {
				setDropdownList(response.data.request_types);
			}
		} catch (error) {
			console.error("Error fetching request data:", error);
		}
	};

	const fetchRequests = async () => {
		try {
			setIsLoading(true);
			const response = await getRequestList({
				org_id: userDetail.organization.id,
				page: queryStrings.page,
				limit: queryStrings.pageSize,
				search: queryStrings.search,
				...appliedFilters, // This will include request_type and status if they exist
				type: userDetail?.role?.name === "SuperAdmin" ? "AllOrganization" : "singleOrganization",
			});
			setRequestData(response.data);
		} catch (error) {
			console.error("Error fetching request list:", error);
		} finally {
			setIsLoading(false);
		}
	};
	const handleOpenModal = (
		type: "approval" | "request",
		data?: UserData,
		actiontype?: "approve" | "reject",
	) => {
		setIsModalOpen((prev) => ({
			...prev,
			[type]: true,
			reqdata: data,
			actiontype: actiontype || "",
		}));
	};

	const handleCloseModal = (type: "approval" | "request") => {
		setIsModalOpen((prev) => ({ ...prev, [type]: false }));
	};

	const toggleField = (id: string) => {
		setVisibleExtraFields((prev) =>
			prev.includes(id) ? prev.filter((key) => key !== id) : [...prev, id],
		);
	};

	const handleFilterApply = (filters: AppliedFilters) => {
		// Convert filter labels to the expected API parameter names
		const apiFilters: AppliedFilters = {};

		if (typeof filters["Request Type"] === "number") {
			apiFilters.request_type = filters["Request Type"];
		}

		if (typeof filters["Status"] === "number") {
			apiFilters.status = filters["Status"];
		}

		setAppliedFilters(apiFilters);
		setQueryString((prev) => ({ ...prev, page: 1 }));
	};

	const handleSearch = (value: string) => {
		setSearchQuery(value);
		setQueryString((prev) => ({ ...prev, search: value, page: 1 }));
	};

	return (
		<>
			<div className="bg-white p-6" style={{ height: "86vh" }}>
				<RequestManagementFilter
					open={isFilterOpen}
					onClose={() => setIsFilterOpen(false)}
					onFilterApply={handleFilterApply}
					initialFilters={appliedFilters}
					requestTypeOptions={dropdownList}
				/>

				<div className="flex justify-between items-center pb-3 gap-2">
					<h1 className="text-xl md:text-2xl font-bold text-[#000000] whitespace-nowrap">Request Approval</h1>
					<div className="flex space-x-2">
						{admin.includes(role?.name) && (
							<CustomButton
								text="Add Request"
								onClick={() => handleOpenModal("request")}
								isLoading={false}
							/>
						)}
						<Button
							variant="outlined"
							size="small"
							onClick={() => setColumnDrawerOpen(true)}
							sx={{whiteSpace:"nowrap"}}
							
						>
							Manage Columns
						</Button>
					</div>
				</div>

				<div className={tailwindStyles?.tablePageHead}>
					<SearchFilter
						searchQuery={searchQuery}
						setSearchQuery={handleSearch}
						onFilterClick={() => setIsFilterOpen(true)}
						placeholder="Search Requests"
					/>

					<TableContainer
						component={Paper}
						style={{ height: "37rem", overflowX: "auto", width: "100%" }}
						className="mt-4 border border-gray-300 rounded-md shadow-sm"
					>
						<Table>
							<TableHead>
								<TableRow>
									<TableCell padding="checkbox" />
									{defaultHeaders?.map((header) => (
										<TableCell
											key={header.id}
											sx={{
												fontWeight: "bold",
												fontSize: "16px",
												fontFamily: "'Open Sans'",
												color: "#475569",
												whiteSpace:"nowrap",
											}}
										>
											{header.name}
										</TableCell>
									))}
									{optionalHeaders
										.filter((header) => visibleExtraFields.includes(header?.id))
										.map((header) => (
											<TableCell
												key={header.id}
												sx={{
													fontWeight: "bold",
													fontSize: "16px",
													fontFamily: "'Open Sans'",
													color: "#475569",
													whiteSpace:"nowrap",
												}}
											>
												{header.name}
											</TableCell>
										))}
									{superadmin.includes(role?.name) && (
										<TableCell>Actions</TableCell>
									)}
								</TableRow>
							</TableHead>

							<TableBody>
								{isLoading ? (
									<TableRow>
										<TableCell
											colSpan={
												defaultHeaders.length + visibleExtraFields.length + 2
											}
											align="center"
										>
											<span className="text-gray-500 text-sm">Loading...</span>
										</TableCell>
									</TableRow>
								) : requestData?.requests?.length === 0 ? (
									<TableRow>
										<TableCell
											colSpan={
												defaultHeaders.length + visibleExtraFields.length + 2
											}
											align="center"
										>
											<span className="text-gray-500 text-sm">
												No requests found.
											</span>
										</TableCell>
									</TableRow>
								) : (
									requestData?.requests?.map((user) => (
										<TableRow key={user?.id} className="hover:bg-gray-50">
											<TableCell padding="checkbox" />
											<TableCell sx={{whiteSpace:"nowrap"}}>{user?.organization_name}</TableCell>
											<TableCell sx={{whiteSpace:"nowrap"}}>{user?.request_type_label}</TableCell>
											<TableCell sx={{whiteSpace:"nowrap"}}>{user?.value}</TableCell>
											<TableCell>{formatDate(user?.created_at)}</TableCell>
											<TableCell>
												<span
													className={`px-3 py-1 rounded-full font-semibold font-[Open Sans] ${user.status_label === "APPROVED"
														? "bg-[#E8F8F3] text-[#10B981]"
														: user.status_label === "PENDING"
															? "bg-[#FFF4E5] text-[#F79009]"
															: user.status_label === "REJECTED"
																? "bg-[#FFECEB] text-[#FF4136]"
																: ""
														}`}
												>
													{user?.status_label
														? user.status_label
															.toLowerCase()
															.replace(/^\w/, (c) => c.toUpperCase())
														: ""}
												</span>
											</TableCell>

											{optionalHeaders
												.filter((header) =>
													visibleExtraFields.includes(header?.id),
												)
												.map((header) => (
													<TableCell key={header.id}>
														{user[header.id as keyof typeof user] || "-"}
													</TableCell>
												))}

											{superadmin.includes(role?.name) && (
												<TableCell>
													<div className="flex items-center space-x-2">
														<button
															type="button"
															onClick={() =>
																handleOpenModal("approval", user, "approve")
															}
															className={
																user.status_label === "PENDING"
																	? tailwindStyles.approveButton
																	: tailwindStyles.disabledCloseIcon
															}
															disabled={user.status_label !== "PENDING"}
														>
															<CheckCircleIcon fontSize="small" />
														</button>

														<button
															type="button"
															onClick={() =>
																handleOpenModal("approval", user, "reject")
															}
															className={
																user.status_label === "PENDING"
																	? tailwindStyles.rejectButton
																	: tailwindStyles.disabledCloseIcon
															}
															disabled={user.status_label !== "PENDING"}
														>
															<CancelIcon fontSize="small" />
														</button>
													</div>
												</TableCell>
											)}
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						queryStrings={queryStrings}
						setQueryString={setQueryString}
						paging={requestData?.pagingInfo}
					/>
				</div>
			</div>

			<Drawer
				anchor="right"
				open={columnDrawerOpen}
				onClose={() => setColumnDrawerOpen(false)}
			>
				<div className="w-64 p-4">
					<h2 className="text-lg font-semibold mb-4">Manage Columns</h2>
					{optionalHeaders?.map((header) => (
						<FormControlLabel
							key={header?.id}
							control={
								<Checkbox
									checked={visibleExtraFields.includes(header?.id)}
									onChange={() => toggleField(header?.id)}
								/>
							}
							label={header?.name}
						/>
					))}
				</div>
			</Drawer>

			<ApprovalModal
				isOpen={isModalOpen.approval}
				reqData={isModalOpen.reqdata}
				onClose={() => handleCloseModal("approval")}
				fetchRequests={fetchRequests}
				actiontype={isModalOpen.actiontype as "approve" | "reject"}
			/>
			<ApprovalRequestModal
				isOpen={isModalOpen.request}
				dropdownList={dropdownList}
				fetchRequests={fetchRequests}
				onClose={() => handleCloseModal("request")}
			/>
		</>
	);
};

export default RequestApprovalPage;