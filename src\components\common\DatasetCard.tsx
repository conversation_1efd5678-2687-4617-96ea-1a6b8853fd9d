import type React from "react";
import { useRouter } from "next/navigation";
import CustomButton from "../ui/Button";
import AvatarFallback from "../ui/AvatarFallback";
import {
  Box,
  IconButton,
  Button,
} from "@mui/material";
import Rating from "@mui/material/Rating";
import ChatOutlinedIcon from '@mui/icons-material/ChatOutlined';
import { useState } from "react";
import RatingModal from "./RatingModal";
import MessageModal from "./MessageModal";

interface DatasetCardProps {
  dataset: Record<string, unknown>;
  className?: string;
  canRead?: boolean;
  datasetId?: string;
  datasetApi: () => Promise<void>;
}

const DatasetCard: React.FC<DatasetCardProps> = ({ dataset, className = "", canRead, datasetApi }) => {
  const router = useRouter();
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const id = dataset?.id ?? dataset?.Id ?? "";
  const title = (dataset?.["Dataset Name"] ?? dataset?.title ?? "Untitled Dataset") as string;
  const author = (dataset?.["Created By"] ?? dataset?.author ?? "Unknown Author") as string;
  const subSpeciality = (dataset?.["Sub Speciality"] ?? dataset?.subSpeciality ?? "") as string;
  const availability = (dataset?.Availability ?? dataset?.availability ?? "N/A") as string;
  const speciality = (dataset?.Speciality ?? dataset?.speciality ?? "") as string;
  const average_rating = (dataset?.average_rating ?? 0) as number;

  const handleViewDataset = () => {
    if (!id) {
      console.error("No dataset ID available");
      return;
    }
    router.push(`/datasets/view?id=${id}`);
  };

  return (
    <>
      {isMessageModalOpen && (
        <MessageModal
          open={isMessageModalOpen}
          onClose={() => setIsMessageModalOpen(false)}
          datasetId={String(dataset.id)}
        />
      )}

      <RatingModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        name={"Dataset"}
        datasetId={String(dataset.id)}
        datasetApi={datasetApi}
      />

      <div className={`flex flex-col md:flex-row p-4 border border-orange-200 bg-white shadow-sm rounded-lg hover:shadow-md transition-shadow ${className}`}>
        <div className="mb-3 sm:mb-0 sm:mr-4">
          <AvatarFallback
            name={title}
            className="w-[40px] h-[40px] sm:w-[50px] sm:h-[50px] md:w-[60px] md:h-[60px] lg:w-[70px] lg:h-[70px] p-2 rounded-lg"
          />
        </div>

        <div className="flex-1 min-w-0 mr-4 gap-x-10">
          <div className="flex items-center flex-wrap">
            <h3 className="text-orange-500 font-semibold font-[Open Sans] whitespace-nowrap text-capitalize">
              {title}
            </h3>

            <Box className="ml-[15px]">
              <Rating value={average_rating} precision={1} readOnly  sx={{
										'& .MuiRating-iconFilled': {
											color: '#16AFB5',
										},
										'& .MuiRating-iconEmpty': {
											color: '#16AFB5',
										},
									}} />
            </Box>
          </div>

          <p className="text-[#02363D] text-[14px] sm:text-md font-[Open_Sans] whitespace-nowrap">
            {author}
            {speciality && ` - ${speciality}`}
            {subSpeciality && ` - ${subSpeciality}`}
          </p>

          {availability && availability !== "N/A" && (
            <p className="text-[#004FB0] text-sm  font-[Open_Sans] mt-1">
              Availability Until: {availability}
            </p>
          )}
        </div>


          <div className="mt-3 lg:mt-0 self-end md:self-center flex flex-row gap-2">
          {canRead && (<>
            <Button
              variant="outlined"
              className="text-transform-none"
              sx={{
                minWidth: "100px",
                height: "35px",
                backgroundColor: "#FFFFFF",
                borderColor: "#F6692F",
                color: "#F6692F",
                "&:hover": {
                  backgroundColor: "#FFF5E5",
                  borderColor: "#FF8C00",
                  color: "#FF8C00",
                },
                fontFamily: "Open Sans",
                fontWeight: "bold",
                whiteSpace: "nowrap",
              }}
              onClick={() => setIsModalOpen(true)}
            >
              Rate Dataset
            </Button>

            <IconButton onClick={() => setIsMessageModalOpen(true)}>
              <ChatOutlinedIcon fontSize="medium" sx={{ color: "gray" }} />
            </IconButton>
            </>
          )}
            <CustomButton
              text="View"
              onClick={handleViewDataset}
              isLoading={false}
              size="small"
              className="min-w-[80px]"
              disabled={!id}
            />
          </div>
     
      </div>
    </>
  );
};

export default DatasetCard;
