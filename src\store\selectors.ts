import { createSelector } from "@reduxjs/toolkit";
import type { RootState } from "./store";

export const selectAccessRights = (state: RootState) => state.accessRights;

export const selectModules = createSelector(
  [selectAccessRights],
  (accessRights) => accessRights.accessRightsList.modules || []
);

export const selectAccessRightsLoading = createSelector(
  [selectAccessRights],
  (accessRights) => accessRights.loading
);