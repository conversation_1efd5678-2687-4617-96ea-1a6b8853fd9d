// components/RolesTab.tsx
import React from "react";
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import type { Role } from "../../components/interface/rolesInterface";

interface RolesTabProps {
  rolesListData: Role[];
  handleEdit: (role: Role) => void;
  handleOpenDeleteModal: (id: string) => void;
}

const RolesTab: React.FC<RolesTabProps> = ({ rolesListData, handleEdit, handleOpenDeleteModal }) => (
  <TableContainer component={Paper} className="shadow-none rounded-lg px-4 py-2 mt-20">
    <Table sx={{ width: "90%", margin: "auto", border: "1px solid #d1d5db" }}>
      <TableHead>
        <TableRow sx={{ borderBottom: "2px solid #d1d5db" }}>
          <TableCell>S.No</TableCell>
          <TableCell>Role Name</TableCell>
          <TableCell>Description</TableCell>
          <TableCell>Action</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {rolesListData.map((role, index) => (
          <TableRow key={role.id} className={index % 2 === 1 ? "bg-[#FEF4ED]" : ""}>
            <TableCell>{index + 1}</TableCell>
            <TableCell>{role.name}</TableCell>
            <TableCell>{role.description || "No description"}</TableCell>
            <TableCell>
              <IconButton onClick={() => handleEdit(role)}>
                <EditOutlinedIcon />
              </IconButton>
              <IconButton onClick={() => handleOpenDeleteModal(role.id)}>
                <DeleteOutlinedIcon />
              </IconButton>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </TableContainer>
);

export default RolesTab;
