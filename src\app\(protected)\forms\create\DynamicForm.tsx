"use client";
import type React from "react";
import { useEffect, useRef, useState } from "react";
import { Box, Button, Grid } from "@mui/material";
import Sidebar from "../../../../components/formBuilder/Sidebar";
import FormBuilder from "../../../../components/formBuilder/FormBuilder";
import FieldEditor from "../../../../components/formBuilder/FieldEditor";
import FormPreview from "../../../../components/formBuilder/FormPreview";
import "./style.css";
import SectionEditor from "../../../../components/formBuilder/sectionEditor";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SideDrawer from "../../../../components/popup/drawer";
import { useRouter, useSearchParams } from "next/navigation";
import { getFieldTypeName } from "../../../../constants/fieldtype";
import ConfirmDialog from "../../../../components/popup/alertPopup";
import { toast } from "react-toastify";
import Loader from "../../../../components/loader/loader";
import { desFormCreate, desFormGet, desFormUpdate } from "../../../../services/desApiService";
import Link from "next/link";
type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
interface Section {
	id: number;
	label: string;
	position: number;
	is_default: boolean;
}
interface FormValues {
	[key: string]: string | number | boolean | File | File[] | null | undefined;
}
interface Field {
	id: number;
	label: string;
	type: FieldType;
	placeholder: string;
	textfieldtype: string;
	min?: number;
	max?: number;
	dropdown_type?: string;
	size?: number;
	position?: number;
	required: boolean;
	is_default?: boolean;
	filter?: boolean;
	section_id: number;
	options?: { id: number; value: string }[];
	dateFormat?: "DD/MM/YYYY" | "MM/DD/YYYY" | "YYYY/MM/DD";
	multiple?: boolean;
	accept?: string;
	fullWidth?: boolean;
	mask?: string;
	onClick?: (values: FormValues) => void;
	startIcon?: React.ElementType;
	endIcon?: React.ElementType;
}

const DynamicForm = () => {
	const router = useRouter();
	const [sections, setSections] = useState<Section[]>([]);
	const [fields, setFields] = useState<Field[]>([]);
	// const [formData, setFormData] = useState<Record<string, Record<string, string | number | boolean | null>>>({});
	const [open, setOpen] = useState(false);
	const [selectedField, setSelectedField] = useState<Field | null>(null);
	const [selectedSection, setSelectedSection] = useState<Section | null>(null);
	const [activeSectionId, setActiveSectionId] = useState<number | null>(null);
	const [sectionEditorOpen, setSectionEditorOpen] = useState(false);
	const paperRef = useRef<HTMLDivElement | null>(null);
	const [drawerOpen, setDrawerOpen] = useState(false);
	const [nextSectionNumber, setNextSectionNumber] = useState(1);
	const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
	const [templateid, setTemplateid] = useState<number | null>(null);
	const [loading, setLoading] = useState(false);

	const searchParams = useSearchParams();
	const id = searchParams.get("id");
	const type = searchParams.get("type");
	const name = searchParams?.get("name");


	useEffect(() => {
		const fetchFormData = async () => {
			if (!type) return;
			setLoading(true);
			try {
				const res = await desFormGet({ type: Number(type) });
				setSections(res?.data?.sections || []);
				setTemplateid(res?.data?.id || null);
				setFields(res?.data?.fields || []);
				setLoading(false);
			} catch (err) {
				console.error("Failed to fetch form data:", err);
			}
		};

		fetchFormData();
	}, [type]);

	// const handleChange = useCallback(
	// 	(name: string, value: string | number | boolean | null, sectionId?: number) => {
	// 		setFormData((prevData) => ({
	// 			...prevData,
	// 			[sectionId || "global"]: {
	// 				...(prevData[sectionId || "global"] || {}),
	// 				[name]: value,
	// 			},
	// 		}));
	// 	},
	// 	[],
	// );

	const addSection = () => {
		const newSection: Section = {
			id: Date.now(),
			label: `Section ${nextSectionNumber}`,
			position: nextSectionNumber,
			is_default: true,
		};

		setSections([...sections, newSection]);
		setNextSectionNumber(nextSectionNumber + 1);
		setActiveSectionId(newSection.id);
	};

	const handleOpenDrawer = () => setDrawerOpen(true);

	const handleCloseDrawer = () => {
		setDrawerOpen(false);
	};

	const addField = (type: number) => {
		if (!activeSectionId) {
			setConfirmDialogOpen(true);
			return;
		}

		const sectionFields = fields.filter(
			(field) => field.section_id === activeSectionId,
		);
		const newPosition = sectionFields.length + 1;

		const newField: Field = {
			id: Date.now(),
			label: getFieldTypeName(type),
			type: type as FieldType,
			required: false,
			filter: false,
			placeholder: "Enter New Field",
			textfieldtype: "text",
			min: undefined,
			max: undefined,
			dropdown_type: "single-select",
			is_default: true,
			size: 6,
			position: newPosition,
			dateFormat: "DD/MM/YYYY",
			section_id: activeSectionId,
			options:
				type === 3 || type === 5 || type === 6 || type === 2 || type === 9
					? ["Option 1", "Option 2"].map((option, index) => ({ id: index + 1, value: option }))
					: undefined,
		};

		setFields([...fields, newField]);
		setSelectedField(newField);
	};

	const handleSectionClick = (sectionId: number) => {
		setActiveSectionId((prevId) =>
			prevId === sectionId ? sectionId : sectionId,
		);
	};

	const handleDelete = (id: number) => {
		if (window.confirm("Are you sure you want to delete this item?")) {
			setFields(fields.filter((field) => field.id !== id));
			setSections(sections.filter((section) => section.id !== id));
		}
	};
	const handleSectionEdit = (section: Section) => {
		setSelectedSection(section);
		setSectionEditorOpen(true);
	};

	const handleSectionDelete = (id: number) => {
		if (window.confirm("Delete this section and all its fields?")) {
			setSections(sections.filter((section) => section.id !== id));
			setFields(fields.filter((field) => field.section_id !== id));

			if (activeSectionId === id) {
				setActiveSectionId(null);
			}

		}
	};
	const handleSectionSave = (updatedSection: Section) => {
		setSections(
			sections.map((s) => (s.id === updatedSection.id ? updatedSection : s)),
		);
	};

	const handleEditClick = (field: Field) => {
		setSelectedField(field);
		setOpen(true);
	};
	const handleSave = async () => {
		if (selectedField) {
			setFields(
				fields.map((f) => (f.id === selectedField.id ? selectedField : f)),
			);
		}
		setOpen(false);
	};

	const handlePublish = async () => {
		if (!sections.length || !fields.length) {
			alert("Add at least one section and field before saving.");
			return;
		}

		const payload = {
			sections,
			fields,
			type: type ? Number(type) : null,
			id: templateid ? templateid : undefined,
		};

		try {
			const response = templateid
				? await desFormUpdate(payload)
				: await desFormCreate(payload);
			router.push("/forms");
			toast.success(response.data.message);
		} catch (error) {
			console.error("Save failed:", error);
			toast.error("Failed to save form.");
		}
	};


	const moveSection = (fromIndex: number, toIndex: number) => {
		setSections((prevSections) => {
			const updatedSections = [...prevSections];
			const [moved] = updatedSections.splice(fromIndex, 1);
			updatedSections.splice(toIndex, 0, moved);

			return updatedSections.map((section, index) => ({
				...section,
				position: index + 1,
			}));
		});
	};

	const moveField = (fromIndex: number, toIndex: number, sectionId: number) => {
		setFields((prevFields) => {
			const sectionFields = prevFields
				.filter((field) => field.section_id === sectionId)
				.sort((a, b) => (a.position || 0) - (b.position || 0));

			if (fromIndex === toIndex) return prevFields;

			const [movedField] = sectionFields.splice(fromIndex, 1);
			sectionFields.splice(toIndex, 0, movedField);
			const updatedFields = sectionFields.map((field, index) => ({
				...field,
				position: index + 1,
			}));

			return prevFields
				.filter((field) => field.section_id !== sectionId)
				.concat(updatedFields);
		});
	};

	const constructedJSON = {
		sections,
		fields,
	};

	return (
		<>
			{loading && <Loader />}
			<Box className="flex justify-between items-center px-6 py-4">
				<h1 className="text-xl font-semibold text-[#000000]">{name}</h1>
				<Box className="flex gap-3">
					<Link href="/forms" prefetch>
						<Button variant="outlined" style={{ background: "white", color: "#000", borderColor: "gray" }}>← Back</Button>
					</Link>

					<Button
						variant="contained"
						className="sm:w-auto bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white normal-case flex items-center gap-2"
						onClick={handleOpenDrawer}
					>
						<VisibilityIcon fontSize="small" />
						Preview
					</Button>
				</Box>
			</Box>
			<Box className="bg-white">
				<Grid container spacing={2} style={{ height: "80vh", margin: 0 }}>
					<Grid item xs={2.5}>
						<Sidebar addField={addField} addSection={addSection} />
					</Grid>

					<Grid item xs={8}>
						<div ref={paperRef}>
							<FormBuilder
								sections={sections}
								fields={fields}
								// formData={formData}
								// handleChange={handleChange}
								handleEditClick={handleEditClick}
								moveSection={moveSection}
								handleDelete={handleDelete}
								handleSectionEdit={handleSectionEdit}
								handleSectionDelete={handleSectionDelete}
								handleSectionClick={handleSectionClick}
								activeSectionId={activeSectionId}
								moveField={moveField}
							/>
						</div>
					</Grid>
					<SideDrawer
						isOpen={drawerOpen}
						onClose={handleCloseDrawer}
						title="Form Preview"
					>
						<FormPreview
							constructedJSON={constructedJSON}
							handlePublish={handlePublish}
							id={id ? Number(id) : undefined}
						/>
					</SideDrawer>
					<FieldEditor
						open={open}
						selectedField={selectedField}
						setSelectedField={setSelectedField}
						handleSave={handleSave}
					/>
					<SectionEditor
						open={sectionEditorOpen}
						section={selectedSection}
						onClose={() => setSectionEditorOpen(false)}
						onSave={handleSectionSave}
					/>
					<ConfirmDialog
						open={confirmDialogOpen}
						description="Please select or create a section first to add a field."
						onConfirm={() => setConfirmDialogOpen(false)}
						onClose={() => setConfirmDialogOpen(false)}
					/>
				</Grid>
			</Box>
		</>
	);
};
export default DynamicForm;

