"use client";
import { useEffect, useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Drawer,
	Input<PERSON><PERSON><PERSON>,
} from "@mui/material";
import type { SelectChangeEvent } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import "./style.css";
import FormFieldRenderer from "../../../../components/forms/renderFields";
import type {
	FormConfig,
	FormField,
	FormSection,
} from "../../../../components/interface/formInterface";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import Loader from "../../../../components/loader/loader";
import {
	dataSetChunkUpload,
	dataSetFileUpload,
} from "../../../../services/dataSetapiService";
import AddIcon from "@mui/icons-material/Add";
import { desFormGet, registerCreate } from "../../../../services/desApiService";
import { toast } from "react-toastify";
import { useMemo } from "react";
import UploadDrawerForm from "./uploadeDrawer";
import Link from "next/link";
import { RootState, useAppSelector } from "../../../../store/store";

export default function UploadDataSetForm() {
	const router = useRouter();
	  const userEmail = useAppSelector((state: RootState) => state.user.userDetail.email);
	
	const [formConfig, setFormConfig] = useState<FormConfig>({
		sections: [],
		fields: [],
		type: 0,
	});
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const [loading, setLoading] = useState(false);
	const [open, setOpen] = useState(false);
	const [, setActiveTab] = useState(0);
	const [uploadedFiles, setUploadedFiles] = useState<
		Record<string, { file_path: string; id: string; link?: string; pat?: string }>
	>({});
	console.log("uploadedFiles", uploadedFiles);
	const [drawerContinued, setDrawerContinued] = useState(false);
	useEffect(() => {
  if (userEmail) {
    const createdByField = formConfig.fields.find((f) => f.label === "Created By");

    if (createdByField) {
      formik.setFieldValue(createdByField.id.toString(), userEmail);
    }
  }
}, [userEmail, formConfig.fields]);
	useEffect(() => {
		const fetchFormData = async () => {
			setLoading(true);
			try {
				const desformvalue = (await desFormGet({
					type: 2,
				})) as { data: FormConfig };
				setFormConfig(desformvalue.data);
			} catch (err) {
				console.error("Failed to fetch form data", err);
			} finally {
				setLoading(false);
			}
		};
		fetchFormData();
	}, []);
	const handleOpenDrawer = () => {
		setOpen(true);
	};

	const handleCloseDrawer = () => {
		setOpen(false);
		setActiveTab(0);
	};

	// const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
	// 	setActiveTab(newValue);
	// };
	const { initialValues, validationSchema } = useMemo(() => {
		const initialVals: Record<
			string,
			string | number | boolean | File | File[] | null
		> = {};
		const validationShape: Record<string, Yup.AnySchema> = {};

		for (const field of formConfig.fields) {
			initialVals[field.id] = "";

			if (field.required) {
				switch (field.type) {
					case 1:
						if (field.textfieldtype === "number") {
							let schema = Yup.number()
								.typeError("Must be a valid number")
								.required(`${field.label} is required`);

							if (field.min !== undefined) {
								schema = schema.min(field.min, `Minimum value is ${field.min}`);
							}
							if (field.max !== undefined) {
								schema = schema.max(field.max, `Maximum value is ${field.max}`);
							}
							validationShape[field.id] = schema;
						} else {
							validationShape[field.id] = Yup.string().required(
								`${field.label} is required`,
							);
						}
						break;

					case 6:
						validationShape[field.id] = Yup.string()
							.email("Please enter a valid email")
							.required(`${field.label} is required`);
						break;

					case 3:
						validationShape[field.id] = Yup.mixed().required(
							`${field.label} is required`,
						);
						break;

					case 7:
						validationShape[field.id] = Yup.mixed()
							.required("File upload is required")
							.test("is-valid-file", "Only allowed file types", (value) => {
								if (!value) return false;
								if (typeof value === "object" && "file_path" in value)
									return true;
								if (value instanceof File) {
									const allowedExtensions = ["csv", "xls", "xlsx", "zip"];
									const ext = value.name.split(".").pop()?.toLowerCase();
									return ext ? allowedExtensions.includes(ext) : false;
								}
								return false;
							});
						break;

					default:
						validationShape[field.id] = Yup.string().required(
							`${field.label} is required`,
						);
				}
			}
		}

		return {
			initialValues: initialVals,
			validationSchema: Yup.object(validationShape),
		};
	}, [formConfig.fields]);
	const formik = useFormik({
		enableReinitialize: true,
		initialValues,
		validationSchema,
		onSubmit: async (values) => {
			try {
				const payload = {
					sections: formConfig.sections,
					fields: formConfig.fields.map((field) => {
						const value = values[field.id];
						if (
							field.type === 7 &&
							typeof value === "object" &&
							value !== null &&
							"value" in value
						) {
							return { ...field, value };
						}
						return { ...field, value };
					}),
					type: formConfig.type,
					sampleDataset: uploadedFiles?.upload_file?.file_path
						? uploadedFiles.upload_file.link && uploadedFiles.upload_file.pat
							? {
								link: uploadedFiles.upload_file.link,
								pat: uploadedFiles.upload_file.pat,
							}
							: { path: uploadedFiles.upload_file.file_path }
						: { link: "", pat: "" },
				};
				
				// console.log("Submitting payload:", payload); // Debug log
				
				const res = await registerCreate(payload);
				toast.success("Data set added successfully");
				setSubmitSuccess(true);
				router.push("/datasets");
				const datasetName = res?.data?.fields?.find(
					(f) => f.label === "Dataset Name",
				)?.value;

				// const cleaned = (({ id, ...rest }) => rest)(res.data);
				const { id, ...rest } = res?.data || {};
				console.log("rest", rest);
				const chunkPayload = {
					name: datasetName,
					uuid: id,
					filepath: rest?.sampleDataset?.path || "",
					formData: rest,
				};

				await dataSetChunkUpload(chunkPayload);
			} catch (error) {
				console.error("Registration failed:", error);
			}
		},
	});

	const handleSelectChange = (
		e: SelectChangeEvent<string | string[]>,
		field: FormField,
	) => {
		formik.setFieldValue(field.id.toString(), e.target.value);
	};

	const handleChange = async (
		e:
			| React.ChangeEvent<HTMLInputElement>
			| {
					target: {
						name: string;
						value: string | number | boolean | File | File[];
						type?: string;
						checked?: boolean;
						files?: FileList;
					};
			  },
	) => {
		const { name, value, type, checked, files } = e.target;

		if (files && files.length > 0) {
			const file = files[0];
			const allowedTypes = [
				"text/csv",
				"application/vnd.ms-excel",
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				"application/zip",
				"application/x-zip-compressed",
			];
			const fileExtension = file.name.split(".").pop()?.toLowerCase();

			if (
				!allowedTypes.includes(file.type) &&
				!["csv", "xls", "xlsx", "zip"].includes(fileExtension || "")
			) {
				formik.setFieldError(
					name,
					"Only CSV, ZIP, and Excel files are allowed",
				);
				return;
			}

			try {
				const formData = new FormData();
				formData.append("file", file);

				const uploadRes = await dataSetFileUpload(formData);
				const filePath =
					uploadRes?.data?.file_path?.replace(/^Documents\//, "") || file.name;
				const fileId = uploadRes?.data?.id;

				const fileObj = { file_path: filePath, id: fileId };

				formik.setFieldValue(name, fileObj);
				setUploadedFiles((prev) => ({
					...prev,
					[name]: fileObj,
				}));
			} catch (error) {
				console.error("File upload failed:", error);
				formik.setFieldError(name, "File upload failed");
			}
		} else if (type === "checkbox" || type === "switch") {
			formik.setFieldValue(name, checked);
		} else {
			formik.setFieldValue(name, value);
		}
	};
	const handleUploadedFile = (data: { file_path: string; id: string; link?: string; pat?: string }) => {
		console.log("Received file data:", data); // Debug log
		
		setUploadedFiles((prev) => ({
			...prev,
			upload_file: {
				file_path: data.file_path,
				id: data.id,
				link: data.link || "",
				pat: data.pat || "",
			},
		}));

		const fileUploadField = formConfig.fields.find((field) => field.type === 7);
		if (fileUploadField) {
			formik.setFieldValue(fileUploadField.id.toString(), {
				file_path: data.file_path,
				id: data.id,
				link: data.link || "",
				pat: data.pat || "",
			});
		}
	};
	const handleFileDelete = async (fieldId: string) => {
		const fileData = uploadedFiles[fieldId];
		if (!fileData?.file_path) return;

		setUploadedFiles((prev) => {
			const updated = { ...prev };
			delete updated[fieldId];
			return updated;
		});

		const fileField = formConfig.fields.find(
			(field) => field.id.toString() === fieldId,
		);
		if (fileField) {
			formik.setFieldValue(fieldId, "");
		}
		setDrawerContinued(false);
	};

	return (
		<>
			{loading && <Loader />}
			<div className="min-h-screen flex items-center justify-center p-4 relative d-flex">
				<div className="w-full max-w-5xl">
					<Box className="flex justify-between items-center mb-6 p-4 rounded-t-lg z-50 sticky top-0 sticky backdrop-blur-sm ">
						<Typography variant="h5" className="font-bold">
							Upload Data Sets
						</Typography>
						<Link href="/datasets" prefetch>
							<Button
							className="text-transform-none"
								style={{ background: "white", color: "black" }}
								variant="contained"
								startIcon={<ArrowBackIcon />}
							>
								Back
							</Button>
						</Link>
					</Box>

					<Card className="w-full max-w-5xl backdrop-blur-sm bg-white/90 overflow-auto shadow-xl hover:shadow-4xl transition-shadow duration-300">
						{submitSuccess && (
							<Alert
								severity="success"
								className="mb-4"
								onClose={() => setSubmitSuccess(false)}
							>
								Form submitted successfully!
							</Alert>
						)}

						<form onSubmit={formik.handleSubmit} noValidate>
							{formConfig.sections
								.sort((a, b) => (a.position || 0) - (b.position || 0))
								.map((section: FormSection) => (
									<div key={section.id} className="mb-4">
										<Typography variant="h6" className="mb-4 p-4">
											{section.label}
										</Typography>
										<Divider className="mb-4" />
										<div className="grid pt-4 pl-8 pr-8 grid-cols-1 md:grid-cols-2 gap-4">
											{formConfig.fields
												.filter(
													(field: FormField) => field.section_id === section.id,
												)
												.sort((a, b) => (a.position || 0) - (b.position || 0))
												.map((field: FormField) => (
													<div key={field.id}>
														{field.type !== 7 ? (
															<FormFieldRenderer
																field={{
																	...field,
																	value: formik.values[field.id] as
																		| string
																		| number
																		| boolean
																		| File
																		| File[]
																		| null,
																}}
																error={
																	formik.touched[field.id] &&
																	formik.errors[field.id]
																		? formik.errors[field.id]
																		: ""
																}
																onChange={handleChange}
																onSelectChange={(e) =>
																	handleSelectChange(
																		e as SelectChangeEvent<string | string[]>,
																		field,
																	)
																}
																disabled={field.label === "Created By"}

															/>
														) : (
															<Box>
																{uploadedFiles.upload_file &&
																drawerContinued ? (
																	<Box
																	className="inputBackground"
																		sx={{
																			display: "flex",
																			alignItems: "center",
																			justifyContent: "space-between",
																			border: "1px solid #ddd",
																			borderRadius: "4px",
																			padding: "8px 16px",
																			backgroundColor: "#fafafa",
																		}}
																	>
																		<Typography variant="body1">
																			{uploadedFiles.upload_file.link 
																				? `Repository: ${uploadedFiles.upload_file.link.substring(0, 30)}${uploadedFiles.upload_file.link.length > 30 ? '...' : ''}`
																				: uploadedFiles.upload_file.file_path.length > 30
																					? `${uploadedFiles.upload_file.file_path.slice(0, 30)}...`
																					: uploadedFiles.upload_file.file_path}
																		</Typography>
																		<Button
																			onClick={() =>
																				handleFileDelete("upload_file")
																			}
																			color="error"
																			size="small"
																		>
																			Remove
																		</Button>
																	</Box>
																) : (
																	<>
																		<InputLabel sx={{ paddingBottom: "7px" }}>
																			Upload Dataset{" "}
																			<span className="text-red-500 ml-1">
																				*
																			</span>
																		</InputLabel>
																		<Box
																			className="upload-button inputBackground"
																			onClick={handleOpenDrawer}

																		>
																			<span  style={{ color: "#999", }}>
																				Add Dataset
																			</span>
																			<IconButton size="small" color="default">
																				<AddIcon />
																			</IconButton>
																		</Box>
																	</>
																)}
																{formik.touched[field.id] &&
																	formik.errors[field.id] && (
																		<Typography
																			color="error"
																			variant="body2"
																			sx={{ mt: 1 }}
																		>
																			{formik.errors[field.id] as string}
																		</Typography>
																	)}
															</Box>
														)}
													</div>
												))}
										</div>
									</div>
								))}
							<Box className="flex justify-center p-5">
								<Button
									variant="outlined"
									className="cancel-button sm:w-auto px-6 py-3 mr-5 font-bold  text-transform-none"
								>
									Cancel
								</Button>
								<Button
									type="submit"
									variant="contained"
									style={{
										padding: "10px",
										width: "200px",
										fontSize: "15px",
									}}
									className="sm:w-auto px-6 py-3 bg-gradient-to-r font-bold from-[#F45C24] to-[#FFCB80] text-white text-transform-none"
								>
									Submit
								</Button>
							</Box>
						</form>
					</Card>
				</div>
			</div>
			{/* Drawer starts here */}
			<Drawer
				anchor="right"
				open={open}
				onClose={handleCloseDrawer}
				PaperProps={{
					sx: {
						width: "700px",
						display: "flex",
						flexDirection: "column",
						justifyContent: "space-between",
					},
				}}
			>
				<UploadDrawerForm
					setOpen={setOpen}
					onFileUpload={handleUploadedFile}
					onContinue={() => setDrawerContinued(true)}
				/>
			</Drawer>
		</>
	);
}
