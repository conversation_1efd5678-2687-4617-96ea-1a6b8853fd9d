
export interface Role {
    id: string;
    name: string;
    description: string;
    organization:{
        id: string;
        name: string;
    }
  }
 export interface ApprovalModalProps {
      isOpen: boolean;
      onClose: () => void;
      fetchRoleDataApi: () => void;
      selectedRoleDetails?: Role;
      clearSelectedRoleDetails: () => void;
  }

  export interface DeleteModalProps {
      isOpen: boolean;
      onClose: () => void;
      selectedRoleId?: string;
      fetchRoleDataApi: () => void;
  }

  