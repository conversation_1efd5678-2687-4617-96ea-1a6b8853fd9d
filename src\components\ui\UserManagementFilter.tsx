"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Typography, Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CircularProgress from "@mui/material/CircularProgress";
import type { SelectChangeEvent } from "@mui/material";
import type {  FormFieldWithValue } from "../formBuilder/types";
import FilterFieldRender from "../forms/searchFields";
import "./style.css";

interface FilterItem {
[key: string]: string|number|boolean;
}

interface UserManagementFilterProps {
  open: boolean;
  onClose: () => void;
  onFilterApply?: (filters: FilterItem) => void;
  initialFilters?: FilterItem;
}

const UserManagementFilter: React.FC<UserManagementFilterProps> = ({ 
  open, 
  onClose, 
  onFilterApply, 
  initialFilters = [] 
}) => {
  const [loading, ] = useState<boolean>(false);
  const [formConfig, setFormConfig] = useState<{ fields: FormFieldWithValue[] }>({
    fields: [],
  });

  useEffect(() => {
    if (!open) return;
  
    const staticFields: FormFieldWithValue[] = [
    //   {
    //     id: 1,
    //     label: "Username",
    //     type: 1,
    //     value: "",
    //     filter: true,
    //     multiple: false,
    //     required: false,
    //     placeholder: "Enter username",
    //   },
    //   {
    //     id: 2,
    //     label: "Email",
    //     type: 1,
    //     value: "",
    //     filter: true,
    //     multiple: false,
    //     required: false,
    //     placeholder: "Enter email",
    //   },
      {
        id: 3,
        label: "status",
        type: 3,
        value: "",
        filter: true,
        multiple: false,
        required: false,
        options: [
          { id: 1, value: "Active"},
          { id: 2, value: "Deactivated" },
        ],
        placeholder: "Select status",
      },
      {
        id: 4,
        label: "Last Activity",
        type: 5,
        value: "",
        filter: true,
        multiple: false,
        required: false,
        dateFormat: "YYYY-MM-DD",
        placeholder: "Select date",
      },
      {
        id: 5,
        label: "Role",
        type: 3,
        value: "",
        filter: true,
        multiple: false,
        required: false,
        options: [
          { id: 1, value: "Admin" },
          { id: 2, value: "SuperAdmin" },
          { id: 3, value: "Collaborator" },
          { id: 4, value: "Customer" },
        ],
        placeholder: "Select role",
      },
    ];
    const fieldsWithValues = staticFields.map((field) => {
        const initialValue = initialFilters?.[field.label];
        return {
          ...field,
          value: initialValue ?? (field.multiple ? [] : ""),
        };
      });
  
    setFormConfig({ fields: fieldsWithValues });
  }, [open]);
  

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
  
    
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) => {
        if (field.id.toString() === name) {
          return {
            ...field,
            value: type === "checkbox" ? checked : value,
          };
        }
        return field;
      }),
    }));
  };  

  const handleSelectChange = (
    e: SelectChangeEvent<string | string[]>,
    field: FormFieldWithValue
  ) => {
    const { value } = e.target;
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((f) => {
        if (f.id === field.id) {
          return { ...f, value };
        }
        return f;
      }),
    }));
  };

  const handleDateChange = (fieldId: number, dateValue: string | null) => {
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) => {
        if (field.id === fieldId) {
          return { ...field, value: dateValue || "" };
        }
        return field;
      }),
    }));
  };

  const handleClear = () => {
    setFormConfig((prevConfig) => ({
      ...prevConfig,
      fields: prevConfig.fields.map((field) => ({
        ...field,
        value: field.multiple ? [] : "",
      })),
    }));
    onClose();
    onFilterApply?.({});
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  
    const filteredFields = formConfig.fields
      .filter((field) => {
        if (!field.filter) return false;
        if (Array.isArray(field.value)) return field.value.length > 0;
        return field.value !== "" && field.value !== undefined && field.value !== null;
      })
      .reduce((acc, field) => {
        if (field.value instanceof File) return acc;
  
        let valueToSend: string | number = "";

        if (typeof field.value === "string" || typeof field.value === "number") {
          valueToSend = field.value;
        } else if (typeof field.value === "boolean") {
          valueToSend = field.value ? 1 : 0;
        } else if (Array.isArray(field.value)) {
          valueToSend = field.value.join(",");
        }
  
        if (field.label === "status") {
          valueToSend = field.value === "Active" ? 1 : 2;
        }
  
        acc[field.label] = Array.isArray(valueToSend)
          ? valueToSend.join(",")
          : valueToSend;
  
        return acc;
      }, {} as FilterItem);
  
    onClose();
    onFilterApply?.(filteredFields);
  };
  

  if (!open) return null;

  return (
    <div className="fixed top-0 right-0 h-full w-[350px] bg-white shadow-lg z-[1000]">
      <Box className="flex justify-between items-center p-2 pl-4 pr-4 border-b border-gray-200">
        <Typography variant="h6" className="font-bold text-[#000000]">
          Filter
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>

      {loading ? (
        <div className="flex items-center justify-center h-full">
          <CircularProgress size={80} style={{ color: "#F45C24" }} />
        </div>
      ) : (
        <Card
          sx={{
            overflow: "auto",
            height: "calc(100% - 120px)",
            padding: 2,
          }}
        >
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4">
              {formConfig.fields.map((field) => (
                <div key={field.id}>
                  <FilterFieldRender
                    field={field}
                    onChange={handleChange}
                    onSelectChange={(e) => handleSelectChange(e, field)}
                    onDateChange={(dateValue) => handleDateChange(field.id, dateValue)}
                  />
                </div>
              ))}
            </div>

            <Box className="flex justify-between p-4 border-t absolute bottom-0 left-0 right-0 bg-white border-t-gray-200">
              <Button
              className="text-transform-none"
                variant="outlined"
                onClick={handleClear}
                style={{
                  width: "48%",
                  fontSize: "15px",
                  color: "#F45C24",
                  borderColor: "#F45C24",
                }}
              >
                Clear
              </Button>
              <Button
              className="text-transform-none"
                type="submit"
                variant="contained"
                style={{
                  width: "48%",
                  fontSize: "15px",
                  background: "linear-gradient(to right, #F45C24, #FFCB80)",
                }}
              >
                Apply
              </Button>
            </Box>
          </form>
        </Card>
      )}
    </div>
  );
};

export default UserManagementFilter;