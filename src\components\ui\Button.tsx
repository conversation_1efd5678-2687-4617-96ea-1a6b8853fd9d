import type React from "react";
import { CircularProgress } from "@mui/material";

interface CustomButtonProps {
  text: string;
  onClick?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  size?: string; 
  className?: string;

}

const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  onClick,
  isLoading = false,
  disabled = false,
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={isLoading || disabled}
      className={`bg-gradient-to-r from-orange-600 to-orange-300 text-white px-5 py-1.5  rounded-md font-bold transition-all cursor-pointer duration-300 whitespace-nowrap  text-base
        sm:px-5 sm:py-2 sm:text-base ${
        disabled ? "opacity-50 cursor-not-allowed" : "hover:bg-orange-600"
      } flex items-center justify-center`}
    >
      {isLoading ? <CircularProgress size={20} color="inherit" /> : text}
    </button>
  );
};

export default CustomButton;
