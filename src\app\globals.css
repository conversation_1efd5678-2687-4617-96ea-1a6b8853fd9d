@import "tailwindcss";
@import "@fontsource/open-sans";

:root {
  --background: #ffffff;
  --foreground: #171717;
  color-scheme: light;
    --font-geist-sans: "Open Sans", sans-serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
 @apply font-sans;
}

nextjs-portal {
  display: none;
}

input::-ms-reveal,
input::-ms-clear {
  display: none !important;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Global Styles */
.otp-input {
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 18px;
  border: 1px solid #ccc;
  border-radius: 8px;
}
/* //  Common styles */
.text-capitalize{text-transform: capitalize;}
.text-transform-none{text-transform: none !important;}
.inputBackground {
  background-color: #fff3eb !important;
}

