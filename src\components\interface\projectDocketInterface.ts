export interface CardProps {
  projectId: string;
  modelName: string;
  version: string;
  type: string;
  specialty: string;
  subSpecialty: string;
  submittedOn: string;
  status: string;
  role: string;
  statusColor: string;
  timeCount: number;
  isSelected?: boolean;
  gpu: string;
  onSelect?: () => void;
  canUpdate?: boolean;
  canRead?: boolean;
   datasetApi: () => Promise<void>; // Add this line
   average_rating: number;
   taggingSampleDataset: string;
}