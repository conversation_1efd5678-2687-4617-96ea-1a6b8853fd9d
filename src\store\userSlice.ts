import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

interface UserDetail {
  full_name?: string;
  user_name?: string;
  email?: string;
  role?: {
    name?: string;
  };
  organization?: {
    name?: string;
    id?: string;
  };
  country?: string;
  isAuthenticated?: boolean;
  user_profile_img_path?: string;
}



interface UserState {
  userDetail: UserDetail;

}

const initialState: UserState = {
  userDetail: {},
 
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    userResponse: (state, action: PayloadAction<UserDetail>) => {
      state.userDetail = action.payload;
    },

    clearUserState: (state) => {
      state.userDetail = {};
    },
  },
});

export const {
  userResponse,

  clearUserState,
} = userSlice.actions;

export default userSlice.reducer;
