import type React from "react";
import { TextField, MenuItem, InputLabel } from "@mui/material";

interface CustomTextFieldProps {
  label: string;
  value: string | number;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  select?: boolean;
  options?: { value: number | string; label: string }[];
  error?: boolean;
  helperText?: string;
}
const CustomTextField: React.FC<CustomTextFieldProps> = ({
  label,
  value,
  onChange,
  select = false,
  options = [],
  error = false,
  helperText = "",
}) => {
  return (
    <>
      <InputLabel sx={{ marginBottom: "3px" }}>{label}</InputLabel>
      <TextField
        fullWidth
        variant="outlined"
        size="small"
        value={value}
        onChange={onChange}
        select={select}
        error={error}
        helperText={helperText}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderRadius: "6px",
            marginBottom: "10px",
          },
        }}
      >
        {select &&
          options?.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
      </TextField>
    </>
  );
};


export default CustomTextField;
